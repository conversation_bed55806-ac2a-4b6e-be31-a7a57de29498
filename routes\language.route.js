const express = require("express");
const validate = require("../middlewares/validate");
const { LanguageValidation } = require("../validations");
const { LanguageController } = require("../controllers");
const auth = require("../middlewares/auth");
const catchAsync = require("../utils/catchAsync");

/**
 * @swagger
 * tags:
 *   name: Language Manager
 *   description: Language management and retrieval
 */
const router = express.Router();

/**
 * @swagger
 * /language:
 *   get:
 *     summary: Get all languages
 *     tags: [Language Manager]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of languages
 *         content:
 *           application/json:
 *             example:
 *               status: true
 *               message: "Languages retrieved successfully"
 *               data:
 *                 - language_id: "123e4567-e89b-12d3-a456-426614174000"
 *                   name: "English"
 *                   code: "en"
 *                   default: true
 *                   status: true
 */
router.get("/", auth("view_languages"), catchAsync(LanguageController.index));

/**
 * @swagger
 * /language:
 *   post:
 *     summary: Create a new language
 *     tags: [Language Manager]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Language'
 *     responses:
 *       201:
 *         description: Language created successfully
 *         content:
 *           application/json:
 *             example:
 *               status: true
 *               message: "Language created successfully"
 *               data:
 *                 language_id: "123e4567-e89b-12d3-a456-426614174000"
 *                 name: "English"
 *                 code: "en"
 *                 default: true
 *                 status: true
 */
router.post("/", auth("create_language"), validate(LanguageValidation.create), catchAsync(LanguageController.create));

/**
 * @swagger
 * /language/{languageId}:
 *   put:
 *     summary: Update a language
 *     tags: [Language Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: languageId
 *         required: true
 *         schema:
 *           type: string
 *         description: Language ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Language'
 *     responses:
 *       200:
 *         description: Language updated successfully
 *         content:
 *           application/json:
 *             example:
 *               status: true
 *               message: "Language updated successfully"
 */
router.put("/:languageId", auth("edit_languages"), validate(LanguageValidation.update), catchAsync(LanguageController.update));

/**
 * @swagger
 * /language/{languageId}/status:
 *   patch:
 *     summary: Update language status
 *     tags: [Language Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: languageId
 *         required: true
 *         schema:
 *           type: string
 *         description: Language ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Language status updated successfully
 *         content:
 *           application/json:
 *             example:
 *               status: true
 *               message: "Language status updated successfully"
 */
router.patch("/:languageId/status", auth("status_of_languages"), validate(LanguageValidation.status), catchAsync(LanguageController.status));

module.exports = router;
