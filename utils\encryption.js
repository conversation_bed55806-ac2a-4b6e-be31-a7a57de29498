const CryptoJS = require('crypto-js');
const logger = require('../config/logger');

// Use environment variable for encryption key, fallback to JWT_SECRET
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || process.env.JWT_SECRET || 'default-key-change-in-production-please';

/**
 * Encrypt a value using AES encryption with crypto-js
 * @param {string} text - The text to encrypt
 * @returns {string} - Encrypted text
 */
function encrypt(text) {
  if (!text || typeof text !== 'string') {
    return text;
  }

  try {
    const encrypted = CryptoJS.AES.encrypt(text, ENCRYPTION_KEY).toString();
    return encrypted;
  } catch (error) {
    logger.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt a value using AES decryption with crypto-js
 * @param {string} encryptedData - Encrypted data
 * @returns {string} - Decrypted text
 */
function decrypt(encryptedData) {
  if (!encryptedData || typeof encryptedData !== 'string') {
    return encryptedData;
  }

  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);

    // If decryption fails, it returns empty string
    if (!decryptedText) {
      logger.warn('Decryption failed, returning original data (might be unencrypted)');
      return encryptedData;
    }

    return decryptedText;
  } catch (error) {
    logger.error('Decryption error:', error);
    // Return original data if decryption fails (might be unencrypted legacy data)
    return encryptedData;
  }
}

/**
 * Check if a string appears to be encrypted (base64 format)
 * @param {string} value - The value to check
 * @returns {boolean} - True if appears encrypted
 */
function isEncrypted(value) {
  if (!value || typeof value !== 'string') {
    return false;
  }

  try {
    // Check if it's valid base64 and has minimum length for encrypted data
    const decoded = Buffer.from(value, 'base64');
    return decoded.length >= (IV_LENGTH + TAG_LENGTH + 1);
  } catch {
    return false;
  }
}

/**
 * Safely decrypt a value, handling both encrypted and plain text
 * @param {string} value - The value to decrypt
 * @param {boolean} isMarkedEncrypted - Whether the value is marked as encrypted in DB
 * @returns {string} - Decrypted or original value
 */
function safeDecrypt(value, isMarkedEncrypted = false) {
  if (!value) {
    return value;
  }

  // If marked as encrypted in DB, attempt decryption
  if (isMarkedEncrypted && isEncrypted(value)) {
    return decrypt(value);
  }

  // Return as-is if not marked as encrypted
  return value;
}

/**
 * Encrypt sensitive agent setting values
 * @param {string} key - The setting key
 * @param {string} value - The setting value
 * @returns {Object} - {value, isEncrypted}
 */
function encryptAgentSetting(key, value) {
  const sensitiveKeys = [
    'password',
    'secret_access_key',
    'access_key_id',
    'connection_string',
    'api_key',
    'token',
    'private_key',
    'cert',
    'certificate'
  ];

  const shouldEncrypt = sensitiveKeys.some(sensitiveKey =>
    key.toLowerCase().includes(sensitiveKey)
  );

  if (shouldEncrypt && value) {
    return {
      value: encrypt(value),
      isEncrypted: true
    };
  }

  return {
    value: value,
    isEncrypted: false
  };
}

module.exports = {
  encrypt,
  decrypt,
  safeDecrypt,
  isEncrypted,
  encryptAgentSetting
};
