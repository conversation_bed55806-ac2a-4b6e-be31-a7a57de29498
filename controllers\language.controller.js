const { Language } = require("../models");
const catchAsync = require("../utils/catchAsync");
const { sendSuccess } = require("../utils/ApiResponse");
const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");
const { paginate } = require("../utils/plugins/paginate");

/**
 * Get all languages with pagination
 * @async
 * @function index
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<void>} Sends a paginated response with the list of languages
 */
exports.index = catchAsync(async (req, res) => {
  const { page, limit, sortBy, sortOrder } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [["updatedAt", "DESC"]],
  };

  const result = await paginate(Language, queryOptions, paginationOptions);
  sendSuccess(res, "Languages retrieved successfully", result);
});

/**
 * Create a new language
 * @async
 * @function create
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<void>} Sends a response with the created language
 */
exports.create = catchAsync(async (req, res) => {
  const { transaction } = req;
  const { name, code, default: isDefault, status, updated_by } = req.body;

  const language = await Language.create(
    { name, code, default: isDefault, status, updated_by },
    { transaction }
  );

  sendSuccess(res, "Language created successfully", language, httpStatus.CREATED);
});

/**
 * Update a language
 * @async
 * @function update
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<void>} Sends a response confirming the update
 */
exports.update = catchAsync(async (req, res) => {
  const { transaction } = req;
  const { languageId } = req.params;
  const { updated_by, ...updateData } = req.body;

  const language = await Language.findByPk(languageId);
  if (!language) {
    throw new ApiError(httpStatus.NOT_FOUND, "Language not found");
  }

  await language.update({ ...updateData, updated_by }, { transaction });
  sendSuccess(res, "Language updated successfully", language);
});

/**
 * Update language status
 * @async
 * @function status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Promise<void>} Sends a response confirming the status update
 */
exports.status = catchAsync(async (req, res) => {
  const { transaction } = req;
  const { languageId } = req.params;
  const { status, updated_by } = req.body;

  const language = await Language.findByPk(languageId);
  if (!language) {
    throw new ApiError(httpStatus.NOT_FOUND, "Language not found");
  }

  await language.update({ status, updated_by }, { transaction });
  sendSuccess(res, "Language status updated successfully", language);
});
