const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Device = sequelize.define(
    "Device",
    {
      device_id: { // primary key of the table
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      identifier: { // actual device id
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
      },
      kiosk_group_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "kiosk_group",
          key: "kiosk_group_id",
        },
        onDelete: "CASCADE",
      },
      facility_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "facility",
          key: "facility_id",
        },
        onDelete: "SET NULL",
      },
      facility_building_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "building",
          key: "building_id",
        },
        onDelete: "SET NULL",
      },
      facility_floor_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "floor",
          key: "floor_id",
        },
        onDelete: "SET NULL",
      },
      facility_room_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "room",
          key: "room_id",
        },
        onDelete: "SET NULL",
      },
    },
    {
      tableName: "device",
      timestamps: true,
      underscored: true,
    }
  );

  Device.associate = (models) => {
    Device.belongsTo(models.KioskGroup, {
      foreignKey: "kiosk_group_id",
      as: "kiosk_group",
      onDelete: "CASCADE",
    });
    Device.belongsTo(models.Facility, {
      foreignKey: "facility_id",
      as: "facility",
      onDelete: "SET NULL",
    });
    Device.belongsTo(models.Building, {
      foreignKey: "facility_building_id",
      as: "building",
      onDelete: "SET NULL",
    });
    Device.belongsTo(models.Floor, {
      foreignKey: "facility_floor_id",
      as: "floor",
      onDelete: "SET NULL",
    });
    Device.belongsTo(models.Room, {
      foreignKey: "facility_room_id",
      as: "room",
      onDelete: "SET NULL",
    });
  };

  history(Device, sequelize, DataTypes);

  return Device;
};
