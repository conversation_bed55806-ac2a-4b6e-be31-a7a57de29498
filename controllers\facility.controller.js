const { Facility, Address, MasterData, Country, State, EventTrace } = require("../models");
const catchAsync = require("../utils/catchAsync");
const { paginate } = require("../utils/plugins/paginate");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { status: httpStatus } = require("http-status");
const ApiError = require("../utils/ApiError");

// Helper function to validate state-country relationship
const validateStateCountry = async (state_id, country_id) => {
    console.log("validateStateCountry");
  const state = await State.findByPk(state_id);
  
  if (!state || state.country_id !== country_id) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "The state does not belong to the given country."
    );
  }
};

// Updated include options to use country and state associations
const includeOptions = [
  {
    model: Address,
    as: "address",
    include: [
      { model: Country, as: "country", attributes: ["name"] },
      { model: State, as: "state", attributes: ["name"] },
    ],
  },
  {
    model: MasterData,
    as: "facility_status_name",
    attributes: ["key", "value"],
  },
  {
    model: MasterData,
    as: "facility_type_name",
    attributes: ["key", "value"],
  },
];

/**
 * Get all facilities with their addresses and master data values using global pagination.
 *
 * @async
 * @function index
 * @param {Object} req - Express request object. Accepts query parameters for pagination.
 * @param {Object} res - Express response object.
 * @param {Function} next - Express next middleware function.
 * @returns {Promise<void>} Sends a paginated response with facility data.
 */
exports.index = catchAsync(async (req, res, next) => {
  const { page, limit, sortBy, sortOrder } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [["updatedAt", "DESC"]],
    include: includeOptions,
  };

  const result = await paginate(Facility, queryOptions, paginationOptions);
  sendSuccess(res, "Facilities retrieved successfully", httpStatus.OK, result);
});

/**
 * Get a facility by ID with its address and master data values.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @param {Function} next - Express next middleware function.
 * @returns {Promise<void>} Sends a response with the facility data or a 404 error if not found.
 */
exports.show = catchAsync(async (req, res, next) => {
  const { facilityId } = req.params;
  const facility = await Facility.findByPk(facilityId, {
    include: includeOptions,
  });
  if (!facility)
    return sendError(res, "Facility not found", httpStatus.NOT_FOUND);
  sendSuccess(res, "Facility retrieved successfully", httpStatus.OK, facility);
});


exports.fetch = catchAsync(async (req, res, next) => {
  const facilities = await Facility.findAll({
    attributes: ["facility_id", "facility_type", "name"], // Selecting only id, type, and status
    where: { status: 0 }, // Add this line to filter by facility_status
  });

  sendSuccess(res, "Facilities fetched successfully", httpStatus.OK, { data: facilities });
});

exports.facility_type = catchAsync(async (req, res, next) => {
  const facilityTypes = await MasterData.findAll({
    where: {
      group: 'facility_type'
    },
    attributes: ['value'],
  });
  const facilityTypeValues = facilityTypes.map(type => type.value);
  sendSuccess(res, "Facility types retrieved successfully", httpStatus.OK, facilityTypeValues);
})

/**
 * Create a facility along with its address.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} req.body - Request body containing facility and address details.
 * @param {Object} res - Express response object.
 * @param {Function} next - Express next middleware function.
 * @returns {Promise<void>} Sends a response with the created facility data.
 */
exports.create = catchAsync(async (req, res, next) => {
  const { transaction, traceContext } = req;

  const {
    name,
    image,
    facility_code,
    facility_type,
    timezone_id,
    phone,
    email,
    geo_location_code,
    other_code,
    facility_url,
    connected_applications,
    notes,
    status,
    address,
    updated_by,
  } = req.body;
  if (address.state_id)
    await validateStateCountry(address.state_id, address.country_id);

  const facility = await Facility.create(
    {
      name,
      image,
      facility_code,
      facility_type,
      timezone_id,
      phone,
      email,
      geo_location_code,
      other_code,
      status,
      facility_url,
      connected_applications,
      notes,
      updated_by,
    },
    { transaction, traceContext }
  );

  await Address.create(
    {
      ...address,
      facility_id: facility.facility_id,
      country_id: address.country_id,
      state_id: address.state_id,
      updated_by,
    },
    { transaction }
  );

  sendSuccess(res, "Facility created successfully", httpStatus.CREATED);
});

/**
 * Update facility details.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.body - Request body containing facility details to update.
 * @param {Object} req.params - Request parameters containing the facility ID.
 * @param {Object} res - Express response object.
 * @param {Function} next - Express next middleware function.
 * @returns {Promise<void>} Sends a response confirming the update or a 404 error if the facility is not found.
 */
exports.update = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId } = req.params;
  const [updated] = await Facility.update(req.body, {
    where: { facility_id: facilityId },
    transaction,
    individualHooks: true,
  });
  sendSuccess(res, "Facility updated successfully", httpStatus.OK);
});

/**
 * Update address details.
 *
 * @async
 * @function address
 * @param {Object} req - Express request object.
 * @param {Object} req.body - Request body containing address details to update.
 * @param {Object} req.params - Request parameters containing the facility ID.
 * @param {Object} res - Express response object.
 * @param {Function} next - Express next middleware function.
 * @returns {Promise<void>} Sends a response confirming the address update or a 404 error if not found.
 */
exports.address = catchAsync(async (req, res, next) => {
  console.log("Address update request body:");
  const transaction = req.transaction;
  const { facilityId } = req.params;
  const { country_id, state_id, ...addressData } = req.body;
  

  if (state_id) {
    await validateStateCountry(state_id, country_id);
  }
  console.log("Validate after controller:");
  const [updated] = await Address.update(
    { ...addressData, country_id, state_id },
    {
      where: { facility_id: facilityId },
      transaction,
    }
  );
  if (!updated)
    return sendError(res, "Address not found", httpStatus.NOT_FOUND);
  sendSuccess(res, "Address updated successfully", httpStatus.OK);
});

/**
 * Change facility status.
 *
 * @async
 * @function status
 * @param {Object} req - Express request object.
 * @param {Object} req.body - Request body containing the new status.
 * @param {Object} req.params - Request parameters containing the facility ID.
 * @param {Object} res - Express response object.
 * @param {Function} next - Express next middleware function.
 * @returns {Promise<void>} Sends a response confirming the status update or a 404 error if the facility is not found.
 */
exports.status = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId } = req.params;
  const [updated] = await Facility.update(req.body, {
    where: { facility_id: facilityId },
    transaction,
    individualHooks: true,
  });
  if (!updated)
    return sendError(res, "Facility not found", httpStatus.NOT_FOUND);
  sendSuccess(res, "Facility status updated successfully", httpStatus.OK);
});

/**
 * Update facility image by facility ID.
 *
 * @async
 * @function image
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Request parameters.
 * @param {string} req.params.facilityId - The facility ID.
 * @param {Object} req.body - Request body.
 * @param {string} req.body.image - The new image URL.
 * @param {Object} res - Express response object.
 * @param {Function} next - Express next middleware.
 * @returns {Promise<void>} Returns the updated facility image data.
 */
exports.image = catchAsync(async (req, res, next) => {

  const { facilityId } = req.params;
  const { image } = req.body;

  console.log("Facility image update request body:", req.body);
  // Get the facility instance
  const facility = await Facility.findByPk(facilityId);
  if (!facility)
    return sendError(res, "Facility not found", httpStatus.NOT_FOUND);

  // Update image through instance to trigger hooks
  facility.image = image; // This will be captured by media plugin's beforeValidate hook

  console.log("Facility image before save:", facility.image);
  await facility.save();
    console.log("Facility image after save:", facility.image);

  sendSuccess(res, "Facility image updated successfully", httpStatus.OK);
});
