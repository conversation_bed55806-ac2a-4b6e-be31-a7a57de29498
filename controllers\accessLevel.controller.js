const { AccessLevel, Facility, System } = require("../models");
const catchAsync = require("../utils/catchAsync");
const { paginate } = require("../utils/plugins/paginate");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { status: httpStatus } = require("http-status");

/**
 * Get all access levels with their associated Facility (name) and System (name).
 *
 * @async
 * @function index
 * @param {Object} req - Express request object (may include query parameters for pagination).
 * @param {Object} res - Express response object.
 */
exports.index = catchAsync(async (req, res, next) => {
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };

  // Include Facility and System details (only facility_id, name and system_id, name)
  const queryOptions = {
    order: [['updatedAt', 'DESC']],
    include: [
      {
        model: Facility,
        as: "facility",
        attributes: ["facility_id", "name"],
      },
      {
        model: System,
        as: "system",
        attributes: ["system_id", "name"],
      },
    ],
  };

  const result = await paginate(AccessLevel, queryOptions, paginationOptions);
  sendSuccess(
    res,
    "Access levels retrieved successfully",
    httpStatus.OK,
    result
  );
});

/**
 * Get an access level by ID along with Facility and System names.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
exports.show = catchAsync(async (req, res, next) => {
  const { accessLevelId } = req.params;
  const accessLevel = await AccessLevel.findByPk(accessLevelId, {
    include: [
      {
        model: Facility,
        as: "facility",
        attributes: ["facility_id", "name"],
      },
      {
        model: System,
        as: "system",
        attributes: ["system_id", "name"],
      },
    ],
  });

  if (!accessLevel) {
    return sendError(res, "Access level not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(
    res,
    "Access level retrieved successfully",
    httpStatus.OK,
    accessLevel
  );
});

/**
 * Create a new access level.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
exports.create = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const {
    name,
    description,
    pacs_access_level_id,
    system_id,
    status,
    access_level_type,
    facility_id,
    updated_by,
  } = req.body;

  const accessLevel = await AccessLevel.create(
    {
      name,
      description,
      pacs_access_level_id,
      system_id,
      status,
      access_level_type,
      facility_id,
      updated_by
    },
    { transaction }
  );

  // Retrieve the newly created access level with associated Facility and System details.
  const createdAccessLevel = await AccessLevel.findByPk(
    accessLevel.access_level_id,
    {
      include: [
        {
          model: Facility,
          as: "facility",
          attributes: ["facility_id", "name"],
        },
        {
          model: System,
          as: "system",
          attributes: ["system_id", "name"],
        },
      ],
    }
  );

  sendSuccess(
    res,
    "Access level created successfully",
    httpStatus.CREATED,
    createdAccessLevel
  );
});

/**
 * Update an existing access level.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
exports.update = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { accessLevelId } = req.params;
  const [updated] = await AccessLevel.update(req.body, {
    where: { access_level_id: accessLevelId },
    transaction,
  });
  if (!updated) {
    return sendError(res, "Access level not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Access level updated successfully", httpStatus.OK);
});

/**
 * Change the status of an access level.
 *
 * @async
 * @function status
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
exports.status = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { accessLevelId } = req.params;
  const [updated] = await AccessLevel.update(
    req.body,
    { where: { access_level_id: accessLevelId }, transaction }
  );
  if (!updated) {
    return sendError(res, "Access level not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(
    res,
    "Access level status updated successfully",
    httpStatus.OK
  );
});
