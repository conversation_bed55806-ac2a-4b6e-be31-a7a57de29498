const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define(
    "Notification",
    {
      notification_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      schema: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      schema_column: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      language: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "notification",
      timestamps: true,
      underscored: true,
    }
  );

  Notification.associate = (models) => {
    Notification.hasMany(models.NotificationChannel, {
      foreignKey: "notification_id",
      as: "channels",
    });
  };
  
  history(Notification, sequelize, DataTypes);

  return Notification;
};