
const {
  FacilityAccessLevel,
  AccessLevel,
  Facility,
  Building,
  Floor,
  Room,
} = require("../models");
const catchAsync = require("../utils/catchAsync");
const { paginate } = require("../utils/plugins/paginate");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { status: httpStatus } = require("http-status");

/**
 * Get all facility access levels with associated details.
 *
 * @async
 * @function index
 */


/**
 * Delete a facility access level by ID.
 *
 * @async
 * @function remove
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains `facilityId` and `facilityAccessLevelId`.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response or an error if not found.
 */
exports.remove = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityAccessLevelId } = req.params;
  const record = await FacilityAccessLevel.findByPk(facilityAccessLevelId);
  if (!record) {
    return sendError(res, "Facility access level not found", httpStatus.NOT_FOUND);
  }
  await record.destroy({ transaction });
  sendSuccess(res, "Facility access level deleted successfully", httpStatus.OK);
});
exports.index = catchAsync(async (req, res, next) => {
  const { facilityId } = req.params;
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };

  const queryOptions = {
    order: [['updatedAt', 'DESC']],
    where: { facility_id: facilityId },
    include: [
      {
        model: AccessLevel,
        as: "access_level",
        attributes: ["access_level_id", "name"],
      },
      {
        model: Facility,
        as: "facility",
        attributes: ["facility_id", "name"],
      },
      {
        model: Building,
        as: "building",
        attributes: ["building_id", "name"],
      },
      {
        model: Floor,
        as: "floor",
        attributes: ["floor_id", "floor_number"],
      },
      {
        model: Room,
        as: "room",
        attributes: ["room_id", "room_number"],
      },
    ],
  };

  const result = await paginate(FacilityAccessLevel, queryOptions, paginationOptions);
  sendSuccess(
    res,
    "Facility access levels retrieved successfully",
    httpStatus.OK,
    result
  );
});

/**
 * Get a facility access level by ID with associated details.
 *
 * @async
 * @function show
 */
exports.show = catchAsync(async (req, res, next) => {
  const { facilityId, facilityAccessLevelId } = req.params;
  const record = await FacilityAccessLevel.findOne({
    where: { facility_access_level_id: facilityAccessLevelId, facility_id: facilityId },
    include: [
      {
        model: AccessLevel,
        as: "access_level",
        attributes: ["access_level_id", "name"],
      },
      {
        model: Facility,
        as: "facility",
        attributes: ["facility_id", "name"],
      },
      {
        model: Building,
        as: "building",
        attributes: ["building_id", "name"],
      },
      {
        model: Floor,
        as: "floor",
        attributes: ["floor_id", "floor_number"],
      },
      {
        model: Room,
        as: "room",
        attributes: ["room_id", "room_number"],
      },
    ],
  });

  if (!record) {
    return sendError(
      res,
      "Facility access level not found",
      httpStatus.NOT_FOUND
    );
  }
  sendSuccess(
    res,
    "Facility access level retrieved successfully",
    httpStatus.OK,
    record
  );
});

/**
 * Create a new facility access level.
 *
 * @async
 * @function create
 */
exports.create = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId } = req.params;
  const { building_id, floor_id, room_id } = req.body;

  // If a building_id is provided, ensure the building belongs to the facility.
  if (building_id) {
    const building = await Building.findOne({
      where: { building_id, facility_id: facilityId },
    });
    if (!building) {
      return sendError(
        res,
        "Building not found in the given facility",
        httpStatus.NOT_FOUND
      );
    }
  }

  // If a floor_id is provided, ensure the floor belongs to the facility (and building if provided).
  if (floor_id) {
    const floorWhere = { floor_id, facility_id: facilityId, building_id };
    if (building_id) {
      floorWhere.building_id = building_id;
    }
    const floor = await Floor.findOne({ where: floorWhere });
    if (!floor) {
      return sendError(
        res,
        "Floor not found in the given facility or building",
        httpStatus.NOT_FOUND
      );
    }
  }

  // If a room_id is provided, ensure the room belongs to the facility (and floor/building if provided).
  if (room_id) {
    const roomWhere = { room_id, facility_id: facilityId, building_id };
    if (building_id) {
      roomWhere.building_id = building_id;
    }
    if (floor_id) {
      roomWhere.floor_id = floor_id;
    }
    const room = await Room.findOne({ where: roomWhere });
    if (!room) {
      return sendError(
        res,
        "Room not found in the given facility, building, or floor",
        httpStatus.NOT_FOUND
      );
    }
  }

  // Create the facility access level record.
  const record = await FacilityAccessLevel.create(
    {
      ...req.body,
      facility_id: facilityId,
      requestable_guest: req.body.requestable_guest,
      default_access_guest: req.body.default_access_guest,
      default_access_identity: req.body.default_access_identity,
      identity_type: req.body.identity_type,
    },
    { transaction }
  );

  // Retrieve the newly created record with associations.
  const createdRecord = await FacilityAccessLevel.findByPk(
    record.facility_access_level_id,
    {
      include: [
        {
          model: AccessLevel,
          as: "access_level",
          attributes: ["access_level_id", "name"],
        },
        {
          model: Facility,
          as: "facility",
          attributes: ["facility_id", "name"],
        },
        {
          model: Building,
          as: "building",
          attributes: ["building_id", "name"],
        },
        {
          model: Floor,
          as: "floor",
          attributes: ["floor_id", "floor_number"],
        },
        {
          model: Room,
          as: "room",
          attributes: ["room_id", "room_number"],
        },
      ],
    }
  );

  sendSuccess(
    res,
    "Facility access level created successfully",
    httpStatus.CREATED,
    createdRecord
  );
});
/**
 * Update an existing facility access level.
 *
 * This function updates a facility access level, ensuring that any changes 
 * to `building_id`, `floor_id`, and `room_id` are valid within the given facility.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains `facilityId` and `facilityAccessLevelId`.
 * @param {Object} req.body - Contains the updated fields (`building_id`, `floor_id`, `room_id`, etc.).
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response or an error if validation fails.
 */
exports.update = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityAccessLevelId } = req.params;
  const { building_id, floor_id, room_id } = req.body;

  // Retrieve the existing record without filtering by building_id, floor_id, or room_id.
  const record = await FacilityAccessLevel.findByPk(facilityAccessLevelId);

  if (!record) {
    return sendError(res, "Facility access level not found", httpStatus.NOT_FOUND);
  }

  // Validate the new building_id if provided.
  if (building_id) {
    const buildingExists = await Building.findOne({
      where: { building_id },
    });
    if (!buildingExists) {
      return sendError(res, "Building not found", httpStatus.BAD_REQUEST);
    }
  }

  // Validate the new floor_id if provided.
  if (floor_id) {
    const floorExists = await Floor.findOne({
      where: {
        floor_id,
        building_id: building_id || record.building_id, // Use new or existing building_id
      },
    });
    if (!floorExists) {
      return sendError(res, "Floor not found for the given building", httpStatus.BAD_REQUEST);
    }
  }

  // Validate the new room_id if provided.
  if (room_id) {
    const roomExists = await Room.findOne({
      where: {
        room_id,
        building_id: building_id || record.building_id,
        floor_id: floor_id || record.floor_id,
      },
    });
    if (!roomExists) {
      return sendError(res, "Room not found for the given building or floor", httpStatus.BAD_REQUEST);
    }
  }

  // Perform the update transactionally.
  const [updated] = await FacilityAccessLevel.update(
    {
      ...req.body,
      requestable_guest: req.body.requestable_guest,
      default_access_guest: req.body.default_access_guest,
      default_access_identity: req.body.default_access_identity,
      identity_type: req.body.identity_type,
    },
    {
      where: { facility_access_level_id: facilityAccessLevelId },
      transaction,
    }
  );

  if (!updated) {
    return sendError(res, "Failed to update facility access level", httpStatus.BAD_REQUEST);
  }

  sendSuccess(res, "Facility access level updated successfully", httpStatus.OK);
});
