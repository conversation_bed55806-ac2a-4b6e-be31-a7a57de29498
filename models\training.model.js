const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Training = sequelize.define(
    "Training",
    {
      training_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(200),
        allowNull: false,
      },
      course_number: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      category: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      course_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      recurrence: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      due_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      date_completed: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      score: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
        comment: "Reference to identity model",
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "training",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          fields: ["identity_id"],
        },
        {
          fields: ["status"],
        },
        {
          fields: ["due_date"],
        },
        {
          fields: ["date_completed"],
        },
        {
          fields: ["course_number"],
        },
      ],
    }
  );

  Training.associate = (models) => {
    Training.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });

    Training.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "training_status_name",
      constraints: false,
      scope: {
        group: "training_status",
      },
    });
    Training.belongsTo(models.MasterData, {
      foreignKey: "course_type",
      targetKey: "key",
      as: "course_type_name",
      constraints: false,
      scope: {
        group: "course_type",
      },
    });
    Training.belongsTo(models.MasterData, {
      foreignKey: "recurrence",
      targetKey: "key",
      as: "recurrence_name",
      constraints: false,
      scope: {
        group: "recurrence",
      },
    });
  };

  // Apply plugins
  history(Training, sequelize, DataTypes);

  return Training;
};
