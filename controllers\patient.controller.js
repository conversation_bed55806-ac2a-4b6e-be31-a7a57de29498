const { Patient, Appointment, Facility, PatientAddress, Building, Floor, Room, PatientAppointmentView, PatientHistoryView, PatientIdentifier, Hl7Message } = require('../models');
const catchAsync = require('../utils/catchAsync');
const { paginate } = require('../utils/plugins/paginate');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const { status: httpStatus } = require('http-status');
const { Op } = require('sequelize');
const { formatIdentity } = require('../utils/helpers');;


/**
 * Get all patient appointment details from the view with pagination.
 *
 * @async
 * @function getPatientAppointments
 * @param {Object} req - Express request object. Accepts query parameters for pagination.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with patient appointment data.
 */


exports.search = catchAsync(async (req, res) => {
  const { search = "", type } = req.query;
  const where = {};

  if (search) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
      { preferred_name: { [Op.iLike]: `%${search}%` } },
    ];
  }

  // Build include array for associations
  const includeArray = [
    {
      model: PatientIdentifier, // Reference to the PatientIdentifier model
      as: 'patientIdentifier', // Use the alias defined in the association
      attributes: ['identifier_value'], // Assuming this is where the MRN is stored
    },
  ];

  // Add appointment filter if type is provided
  if (type !== undefined) {
    includeArray.push({
      model: Appointment,
      as: 'appointments', // Use the alias defined in the association
      where: { type: parseInt(type) }, // Filter appointments by type
      attributes: ['appointment_id', 'type', 'appointment_date'], // Include appointment details
      required: true, // Inner join - only patients with matching appointments
    });
  }

  const patients = await Patient.findAll({
    where,
    include: includeArray,
    attributes: [
      "first_name",
      "last_name",
      "image",
      "patient_id",
      "preferred_name",
      "birth_date",
    ],
  });

  const formattedPatients = patients.map(patient => {
    const patientData = patient.toJSON(); // Convert to plain object

    return {
      first_name: patientData.first_name,
      last_name: patientData.last_name,
      image: patientData.image,
      patient_id: patientData.patient_id,
      preferred_name: patientData.preferred_name,
      birth_date: patientData.birth_date ? new Date(patientData.birth_date).toISOString().split('T')[0] : null,
      mrn: patientData.patientIdentifier?.identifier_value || null,
      // Include appointment type if filtered by type
      ...(type !== undefined && patientData.appointments && patientData.appointments.length > 0 && {
        appointment_type: patientData.appointments[0].type,
        appointment_id: patientData.appointments[0].appointment_id,
        appointment_date: patientData.appointments[0].appointment_date ?
          new Date(patientData.appointments[0].appointment_date).toISOString().split('T')[0] : null,
      }),
    };
  });

  return sendSuccess(
    res,
    "Patients retrieved successfully",
    httpStatus.OK,
    formattedPatients
  );
});

exports.view = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search = "", status, sortBy = 'appointment_date', sortOrder = 'DESC' } = req.query; // Destructure pagination and sorting options
  const paginationOptions = { page, limit, sortBy, sortOrder }; // Include sorting options
  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC']], // Dynamic sorting
    attributes: {
      exclude: [
        'provider_name',
        'facility_id',
        'beds',
        'confidentiality_code',
        'birth_date',
        'patient_gender',
        'screening',
        'image',
      ],
    },
  };
  const whereConditions = [];
  if (search) {
    whereConditions.push({
      [Op.or]: [
        { first_name: { [Op.iLike]: `%${search}%` } },
        { last_name: { [Op.iLike]: `%${search}%` } },
      ],
    });
  }
  if (status) {
    whereConditions.push({ appointment_status: status });
  }
  if (whereConditions.length > 0) {
    queryOptions.where = { [Op.and]: whereConditions };
  }
  // Call the paginate function with the PatientAppointmentView model
  const result = await paginate(PatientAppointmentView, queryOptions, paginationOptions);
  sendSuccess(res, 'Patient appointments retrieved successfully', httpStatus.OK, result);
});

exports.single = catchAsync(async (req, res) => {
  const { patient_id } = req.query; // Get patient_id from query parameters
  const queryOptions = {
    where: {
      patient_id, // Filter by patient_id
    },
    attributes: [
      'first_name',
      'last_name',
      'mrn',
      'appointment_type_name',
      'confidentiality_code',
      'facility_name',
      'appointment_status_name',
      'appointment_status',
      'appointment_date',
      'patient_id',
      'image',
    ],
    order: [['appointment_date', 'DESC']],
    limit: 1,
  };
  const result = await PatientAppointmentView.findOne(queryOptions); // Use findOne to fetch a single record
   if (!result) {
    return sendSuccess(res, 'No appointment found for the given patient', httpStatus.OK, []); // Return empty array
  }
  sendSuccess(res, 'Latest patient appointment details retrieved successfully', httpStatus.OK, result);
});

exports.information = catchAsync(async (req, res) => {
  const { patient_id } = req.query; // Get patient_id from query parameters
  if (!patient_id) {
    return sendError(res, 'patient_id is required', httpStatus.BAD_REQUEST);
  }
  const queryOptions = {
    where: {
      patient_id, // Filter by patient_id
    },
    attributes: [
      'first_name',
      'middle_name',
      'last_name',
      'email',
      'preferred_name',
      'phone',
      'birth_date',
      'type',
      'appointment_type_name',
      "arrival_time",
      "departure_time",
      'death_date',
      'confidentiality_code',
      'facility_id',
      'facility_name',
      'building_name',
      'floor_number',
      'room_number',
      'beds',
      'appointment_status_name',
      'appointment_date',
      'address_line_1',
      'address_line_2',
      'country',
      'state',
      'postal_code',
      'city',
      'patient_id',

    ],
    order: [['appointment_date', 'DESC']],
    limit: 1,
  };
  const result = await PatientAppointmentView.findOne(queryOptions); // Use findOne to fetch a single record
  if (!result) {
    return sendError(res, 'No appointment found for the given patient', httpStatus.NOT_FOUND);
  }
  sendSuccess(res, 'Latest patient appointment details retrieved successfully', httpStatus.OK, result);
});

/**
* Update the image for a patient guest.
*
* @param {string} patient_id
* @body {string} image
* @returns {Promise<void>}
*/
exports.image = catchAsync(async (req, res) => {
  const { patient_id } = req.params;
  const patient = await Patient.findByPk(patient_id);
  const { image } = req.body; // or req.file if using multer
  patient.image = image;
  await patient.save();
  return sendSuccess(res, "Patient image updated successfully", httpStatus.OK, patient);
});

/**
 * Update patient details such as first name, last name, middle name, email, phone, preferred name, and birth date.
 *
 * @async
 * @function updatePatientDetails
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response with the updated patient details.
 */
exports.updatePatientDetails = catchAsync(async (req, res) => {
  const { patient_id } = req.params;
  const { first_name, last_name, middle_name, email, phone, preferred_name, birth_date, updated_by } = req.body;

  const patient = await Patient.findByPk(patient_id);
  if (!patient) {
    return sendError(res, "Patient not found", httpStatus.NOT_FOUND);
  }

  await patient.update({ first_name, last_name, middle_name, email, phone, preferred_name, birth_date, updated_by });
  sendSuccess(res, "Patient details updated successfully", httpStatus.OK, patient);
});

/**
 * Update admission details such as type, arrival time, discharge time, death date, and confidentiality code.
 *
 * @async
 * @function updateAdmissionDetails
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response with the updated admission details.
 */
exports.updateAdmissionDetails = catchAsync(async (req, res) => {
  const { patient_id } = req.params;
  const { type, arrival_time, discharge_time, death_date, confidentiality_code } = req.body;
  const appointment = await Appointment.findOne({ where: { patient_id } });
  if (!appointment) {
    return sendError(res, "Appointment not found for the patient", httpStatus.NOT_FOUND);
  }
  await appointment.update({ type, arrival_time, departure_time: discharge_time });

  // Update fields in the Patient model
  const patient = await Patient.findByPk(patient_id);
  if (!patient) {
    return sendError(res, "Patient not found", httpStatus.NOT_FOUND);
  }

  await patient.update({ death_date, confidentiality_code });

  sendSuccess(res, "Admission details updated successfully", httpStatus.OK, { appointment, patient });
});

/**
 * Update facility details such as facility name, building, floor, room, and bed number.
 *
 * @async
 * @function updateFacilityDetails
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response with the updated facility details.
 */
exports.updateFacilityDetails = catchAsync(async (req, res) => {
  const { patient_id } = req.params;
  const {
    facility_id,
    facility_name,
    building_id,
    building_name, //name
    floor_id,
    floor_number,
    room_id,
    room_number,
    beds
  } = req.body;

  // Update Facility
  const facility = await Facility.findByPk(facility_id);
  if (!facility) {
    return sendError(res, "Facility not found", httpStatus.NOT_FOUND);
  }
  await facility.update({ name: facility_name });

  // Update Building
  const buildingRecord = await Building.findByPk(building_id);
  if (buildingRecord) {
    await buildingRecord.update({ name: building_name });
  }

  // Update Floor
  const floorRecord = await Floor.findByPk(floor_id);
  if (floorRecord) {
    await floorRecord.update({ floor_number });
  }

  // Update Room
  const roomRecord = await Room.findByPk(room_id);
  if (roomRecord) {
    await roomRecord.update({ room_number });
  }

  // Update Appointment
  const appointment = await Appointment.findOne({
    where: { patient_id, facility_id }
  });
  if (!appointment) {
    return sendError(
      res,
      "Appointment not found for the patient in the specified facility",
      httpStatus.NOT_FOUND
    );
  }
  await appointment.update({ beds });

  sendSuccess(res, "Facility details updated successfully", httpStatus.OK, {
    facility,
    building: buildingRecord,
    floor: floorRecord,
    room: roomRecord,
    appointment
  });
});


exports.updateAddress = catchAsync(async (req, res) => {
  const { patient_id } = req.params;
  const { address_line_1, address_line_2, country, state, city, postal_code } = req.body;
  // Update fields in the Facility model
  let address = await PatientAddress.findOne({ where: { patient_id } });
  if (address) {
    await address.update({ address_line_1, address_line_2, country, state, city, postal_code });
    return sendSuccess(res, "Address details updated successfully", httpStatus.OK, { address });
  } else {
    // If no address exists, create a new one
    address = await PatientAddress.create({
      patient_id,
      address_line_1,
      address_line_2,
      country,
      state,
      city,
      postal_code,
    });
    return sendSuccess(res, "Address created successfully", httpStatus.CREATED, { address });
  }
});

/**
 * Get patient history from the view based on patient_id.
 *
 * @async
 * @function getPatientHistory
 * @param {Object} req - Express request object. Accepts patient_id as a query parameter.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response with patient history data.
 */
exports.getPatientHistory = catchAsync(async (req, res) => {
  const { patient_id } = req.query;
  // Fetch patient history
  const histories = await PatientHistoryView.findAll({
    where: { patient_id },
    order: [['effective_date', 'DESC']],
  });

  if (!histories || histories.length === 0) {
    return sendError(res, 'No history found for the given patient', httpStatus.NOT_FOUND);
  }

  // Format updated_by for each history entry
  const formattedHistories = await Promise.all(
    histories.map(async (history) => {
      // Convert updated_by to the correct data type if necessary (e.g., string to number)
      const identityId = history.modified_by;

      // Format the identity
      const formattedIdentity = await formatIdentity(identityId);
      return {
        ...history.get({ plain: true }),
        updated_by: formattedIdentity || `Identity not found (ID: ${identityId})`,
      };
    })
  );

  sendSuccess(res, 'Patient history retrieved successfully', httpStatus.OK, formattedHistories);
});
exports.getAllPatientHistory = catchAsync(async (req, res) => {
  const histories = await PatientHistoryView.findAll({
    order: [['effective_date', 'DESC']],
  });

  if (!histories || histories.length === 0) {
    return sendError(res, 'No history found for the given patient', httpStatus.NOT_FOUND);
  }

  // Format updated_by for each history entry
  const formattedHistories = await Promise.all(
    histories.map(async (history) => {
      // Convert updated_by to the correct data type if necessary (e.g., string to number)
      const identityId = history.modified_by;

      // Format the identity
      const formattedIdentity = await formatIdentity(identityId);
      return {
        ...history.get({ plain: true }),
        updated_by: formattedIdentity || `Identity not found (ID: ${identityId})`,
      };
    })
  );

  sendSuccess(res, 'Patient history retrieved successfully', httpStatus.OK, formattedHistories);
});

/**
 * Get paginated HL7 messages filtered by MRN and dynamic search across all searchable fields.
 *
 * @async
 * @function getHl7Messages
 * @param {Object} req - Express request object. Accepts query parameters for pagination and filtering.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with HL7 message data.
 *
 *
 */


exports.getHl7Messages = catchAsync(async (req, res) => {
  // Destructure pagination, filtering, and sorting options
  const {
    page = 1,
    limit = 10,
    search = "",
    sortBy = 'processed_at',
    sortOrder = 'DESC',
  } = req.query;
  const { mrn } = req.params;

  // Build pagination and base query options
  const paginationOptions = { page, limit, sortBy, sortOrder };
  const queryOptions = {
    where: {},
    order: [[sortBy, sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC']],
  };

  // If MRN filter provided, add to where
  if (mrn) {
    queryOptions.where.mrn = mrn;
  }

  // Dynamic search: get all model attributes except audit fields
  const allFields = Object.keys(Hl7Message.rawAttributes);
  const excludedFields = ['hl7_message_id', 'updated_at', 'created_by', 'updated_by'];

  const stringFields = Object.entries(Hl7Message.rawAttributes)
  .filter(([_, attr]) => ['STRING', 'TEXT', 'UUID', 'CHAR'].includes(attr.type.key))
  .map(([field]) => field);
const searchableFields = stringFields.filter(field => !excludedFields.includes(field));

  // Build search conditions
  if (search) {
    queryOptions.where[Op.and] = [
      ...(queryOptions.where[Op.and] || []),
      {
        [Op.or]: searchableFields.map(field => ({
          [field]: { [Op.iLike]: `%${search}%` }
        }))
      }
    ];
  }

  // Execute paginated query
  const result = await paginate(Hl7Message, queryOptions, paginationOptions);
  return sendSuccess(res, 'HL7 messages retrieved successfully', httpStatus.OK, result);
});
