const { State, Country } = require("../models");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { status: httpStatus } = require("http-status");
const catchAsync = require("../utils/catchAsync");

/**
 * @class StateController
 * @description Controller for managing states
 */
const StateController = {
  index: catchAsync(async (req, res) => {
    const states = await State.findAll({ include: { model: Country, as: "country" } });
    sendSuccess(res, "States retrieved successfully", httpStatus.OK, states);
  }),

  show: catchAsync(async (req, res) => {
    const { stateId } = req.params;
    const state = await State.findByPk(stateId, { include: { model: Country, as: "country" } });
    if (!state) return sendError(res, "State not found", httpStatus.NOT_FOUND);
    sendSuccess(res, "State retrieved successfully", httpStatus.OK, state);
  }),

  create: catchAsync(async (req, res) => {
    const state = await State.create(req.body);
    sendSuccess(res, "State created successfully", httpStatus.CREATED, state);
  }),

  update: catchAsync(async (req, res) => {
    const { stateId } = req.params;
    const [updated] = await State.update(req.body, { where: { state_id: stateId } });
    if (!updated) return sendError(res, "Failed to update state", httpStatus.BAD_REQUEST);
    sendSuccess(res, "State updated successfully", httpStatus.OK);
  }),

  delete: catchAsync(async (req, res) => {
    const { stateId } = req.params;
    const deleted = await State.destroy({ where: { state_id: stateId } });
    if (!deleted) return sendError(res, "Failed to delete state", httpStatus.BAD_REQUEST);
    sendSuccess(res, "State deleted successfully", httpStatus.NO_CONTENT);
  }),
};

module.exports = StateController;