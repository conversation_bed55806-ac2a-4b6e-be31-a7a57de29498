const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Delegates = sequelize.define(
    "Delegates",
    {
      delegate_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(200),
        allowNull: false,
      },
      eid: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      task_to_delegate: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      start_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      end_date: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
      },
      created_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "delegates",
      timestamps: true,
      underscored: true,
      indexes: [
        {
          fields: ["identity_id"],
        },
        {
          fields: ["status"],
        },
        {
          fields: ["start_date"],
        },
        {
          fields: ["end_date"],
        },
      ],
    }
  );

  Delegates.associate = (models) => {
    Delegates.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });

    Delegates.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "delegation_status_name",
      constraints: false,
      scope: {
        group: "delegation_status",
      },
    });
  };

  // Apply plugins
  history(Delegates, sequelize, DataTypes);

  return Delegates;
};
