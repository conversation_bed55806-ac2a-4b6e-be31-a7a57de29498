const { status: httpStatus } = require("http-status");
const ApiError = require("../utils/ApiError");
const { Identity, IdentityVerification, Token, ActivityLog, LanguagePreference, Language } = require("../models");
const { tokenTypes } = require("../config/attributes");
const jwt = require("jsonwebtoken");
const moment = require("moment");
const config = require("../config/config");

/**
 * Generate token
 * @param {number} identityId
 * @param {moment} expires
 * @param {string} type
 * @param {string} [secret]
 * @returns {string}
 */
const generateToken = (
  identityId,
  expires,
  type,
  secret = config.jwt.secret
) => {
  const payload = {
    sub: identityId,
    iat: moment().unix(),
    exp: expires.unix(),
    type,
  };
  return jwt.sign(payload, secret);
};

/**
 * Save a token
 * @param {string} token
 * @param {number} identityId
 * @param {moment} expires
 * @param {string} type
 * @param {boolean} [blacklisted]
 * @returns {Promise<Token>}
 * @param {Object} [transaction] - Optional transaction

 */
const saveToken = async (
  token,
  identityId,
  expires,
  type,
  blacklisted = false,
  transaction = null
) => {
  let tokenDoc;
    tokenDoc = await Token.create(
      {
        token,
        identity_id: identityId,
        expires: expires.toDate(),
        type,
        blacklisted,
      },
      { transaction }
    );
  return tokenDoc;
};

/**
 * Verify token and return token doc (or throw an error if it is not valid)
 * @param {string} token
 * @param {string} type
 * @returns {Promise<Token>}
 */
const verifyToken = async (token, type) => {
  const payload = jwt.verify(token, config.jwt.secret);
  const tokenDoc = await Token.findOne({
    where: {
      token,
      type,
      identity_id: payload.sub,
      blacklisted: false,
    },
  });
  if (!tokenDoc) {
    throw new Error("Invalid token");
  }
  return tokenDoc;
};

/**
 * Generate auth tokens
 * @param {Identity} identity
 * @param {Object} [transaction] - Optional transaction
 * @returns {Promise<Object>}
 */
const generateAuthTokens = async (identity, transaction = null) => {
  const accessTokenExpires = moment().add(
    config.jwt.accessExpirationMinutes,
    "minutes"
  );
  const accessToken = generateToken(
    identity.identity_id,
    accessTokenExpires,
    tokenTypes.ACCESS
  );

  const refreshTokenExpires = moment().add(
    config.jwt.refreshExpirationDays,
    "days"
  );
  const refreshToken = generateToken(
    identity.identity_id,
    refreshTokenExpires,
    tokenTypes.REFRESH
  );
  await saveToken(
    refreshToken,
    identity.identity_id,
    refreshTokenExpires,
    tokenTypes.REFRESH,
    false,
    transaction
  );

  return {
    access: {
      token: accessToken,
      expires: accessTokenExpires.toDate(),
    },
    refresh: {
      token: refreshToken,
      expires: refreshTokenExpires.toDate(),
    },
  };
};

/**
 * Load all of an identity's permissions (flattened array of strings)
 * @param {Identity} identity
 * @returns {Promise<string[]>}
 */
const getUserPermissions = async (identity) => {
  // eager‑load roles → permissions
  const roles = await identity.getRole({
    include: [{
      association: 'permission',
      attributes: ['name'],
      through: { attributes: [] },
    }],
  });
  // flatten
  return roles.flatMap(role => role.permission.map(p => p.name));
};

/**
 * Login with username and password
 * @param {string} email
 * @param {string} password
 * @returns {Promise<Identity>}
 */
const loginWithEmailAndPassword = async (email, password) => {
  const identity = await Identity.findOne({ where: { email } });
  if (!identity)
    throw new ApiError(httpStatus.UNAUTHORIZED, "Incorrect email or password");

  // Fetch associated IdentityVerification for password verification
  const identityVerification = await IdentityVerification.findOne({
    where: { identity_id: identity.identity_id },
  });

  if (
    !identityVerification ||
    !(await identityVerification.validatePassword(password))
  ) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Incorrect email or password");
  }

  return identity;
};

/**
 * Logout
 * @param {string} refreshToken
 * @returns {string} identity_id of token
 */
const logout = async (refreshToken, transaction) => {
  const refreshTokenDoc = await Token.findOne({
    where: {
      token: refreshToken,
      type: tokenTypes.REFRESH,
      blacklisted: false,
    },
  });

  if (!refreshTokenDoc) {
    throw new ApiError(httpStatus.NOT_FOUND, "Not found");
  }
  const identity_id = refreshTokenDoc.identity_id;
  await refreshTokenDoc.destroy({transaction});
  return identity_id;
};

/**
 * Refresh auth tokens
 * @param {string} refreshToken
 * @returns {Promise<Object>}
 */
const refreshAuth = async (refreshToken, transaction) => {
  try {
    const refreshTokenDoc = await verifyToken(
      refreshToken,
      tokenTypes.REFRESH
    );
    const identity = await Identity.findOne({
      where: { identity_id: refreshTokenDoc.identity_id },
    });
    if (!identity) throw new Error();
    await refreshTokenDoc.destroy({transaction});
    return {
      identity_id: identity.identity_id,
      tokens: await generateAuthTokens(identity, transaction),
    };
  } catch (error) {
    throw new ApiError(
      httpStatus.UNAUTHORIZED,
      error.message || "Invalid token error"
    );
  }
};

/**
 * Create activity logs of user actions
 * @param {string} identity_id
 * @param {string} action
 * @param {Object} req
 */
const createActivityLog = async (identity_id, action, req, transaction) => {
  await ActivityLog.create({
    identity_id,
    action,
    ip_address: req.ip,
    user_agent: req.headers["user-agent"],
  }, {transaction});
};

/**
 * Get language preference for a user.
 * If no preference exists, return the default language.
 * @param {string} identity_id - The ID of the identity.
 * @returns {Promise<Object>} The language preference or default language.
 */
const getLanguagePreference = async (identity_id) => {
  const languagePreference = await LanguagePreference.findOne({
    where: { identity_id },
    include: [{
      model: Language,
      as: 'language',
    }],
  });

  return languagePreference
    ? languagePreference.language
    : await Language.findOne({ where: { default: true } });
};

module.exports = {
  generateAuthTokens,
  loginWithEmailAndPassword,
  logout,
  refreshAuth,
  createActivityLog,
  getUserPermissions,
  getLanguagePreference
};
