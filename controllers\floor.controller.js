const { Floor, Building, MasterData } = require("../models");
const catchAsync = require("../utils/catchAsync");
const { paginate } = require("../utils/plugins/paginate");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { status: httpStatus } = require("http-status");

/**
 * Get all floors for a specific building (paginated).
 *
 * @async
 * @function index
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the buildingId.
 * @param {Object} req.query - Query parameters for pagination (page and limit).
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with floor data, including the building name.
 */
exports.index = catchAsync(async (req, res, next) => {
  const { facilityId } = req.params;
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };

  const queryOptions = {
    order: [["updatedAt", "DESC"]],
    where: { facility_id: facilityId },
    include: [
      {
        model: Building,
        as: "building",
        attributes: ["name"],
        required: false
      },
      {
        model: MasterData,
        as: "floor_status_name",
        attributes: ["key", "value"],
      },
      {
        model: MasterData,
        as: "floor_occupancy_type_name", // optional if you want to return occupancy type info
        attributes: ["key", "value"],
      },
    ],
  };

  const result = await paginate(Floor, queryOptions, paginationOptions);
  sendSuccess(res, "Floors retrieved successfully", httpStatus.OK, result);
});

/**
 * Get all floors for a specific building (without pagination).
 *
 * @async
 * @function fetch
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the buildingId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response with an array of floor data (only floor_id and name).
 */
exports.fetch = catchAsync(async (req, res, next) => {
  const { buildingId } = req.params;

  const floors = await Floor.findAll({
    where: { building_id: buildingId },
    attributes: ["floor_id", "floor_number"], // Selecting only the floor's id and name
  });

  sendSuccess(res, "Floors fetched successfully", httpStatus.OK, {
    data: floors,
  });
});

/**
 * Get a single floor by its ID for a specific building.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains buildingId and floorId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the floor details with the building name or a 404 error if not found.
 */
exports.show = catchAsync(async (req, res, next) => {
  const { facilityId, floorId } = req.params;
  const floor = await Floor.findOne({
    where: { floor_id: floorId, facility_id: facilityId },
    include: [
      {
        model: Building,
        as: "building",
        attributes: ["name"],
        where: { facility_id: facilityId },
      },
      {
        model: MasterData,
        as: "floor_status_name",
        attributes: ["key", "value"],
      },
      {
        model: MasterData,
        as: "floor_occupancy_type_name", // optional if you want to return occupancy type info
        attributes: ["key", "value"],
      },
    ],
  });
  if (!floor) {
    return sendError(res, "Floor not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Floor retrieved successfully", httpStatus.OK, floor);
});

/**
 * Create a new floor under a specific building.
 * Uses the entire validated request body.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the buildingId.
 * @param {Object} req.body - Contains the floor details.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the created floor data including the building's name.
 */
exports.create = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId } = req.params;
  const { building_id } = req.body;

  // Validate that the building exists in the given facility.
  if (building_id) {
    const building = await Building.findOne({
      where: { building_id, facility_id: facilityId },
      attributes: ["name"],
    });
    if (!building)
      return sendError(
        res,
        "Building not found for the given facility",
        httpStatus.NOT_FOUND
      );
  }

  // Create the floor. The request body already includes building_id.
  const floor = await Floor.create(
    { ...req.body, facility_id: facilityId },
    { transaction }
  );
  sendSuccess(res, "Floor created successfully", httpStatus.CREATED, {
    ...floor.dataValues,
  });
});

/**
 * Update floor details.
 * Uses the whole validated request body.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains buildingId and floorId.
 * @param {Object} req.body - Contains floor fields to update.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response or a 404 error if the floor is not found.
 */
exports.update = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, floorId } = req.params;
  const { building_id } = req.body;

  // Find the floor with matching floor_id and facility_id.
  const floor = await Floor.findOne({
    where: { floor_id: floorId, facility_id: facilityId },
  });

  if (!floor) {
    return sendError(res, "Floor not found", httpStatus.NOT_FOUND);
  }

  // If building_id is provided, validate that the building exists in the facility.
  if (building_id) {
    const building = await Building.findOne({
      where: { building_id, facility_id: facilityId },
      attributes: ["name"],
    });
    if (!building) {
      return sendError(
        res,
        "Building not found for the given facility",
        httpStatus.NOT_FOUND
      );
    }
  }

  // Update the floor using the validated req.body.
  const [updated] = await Floor.update(req.body, {
    where: { floor_id: floorId },
    transaction,
  });

  if (!updated) {
    return sendError(res, "Floor not found", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Floor updated successfully", httpStatus.OK);
});

/**
 * Update floor status.
 *
 * @async
 * @function status
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains buildingId and floorId.
 * @param {Object} req.body - Contains the new status.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response confirming the status update or a 404 error if the floor is not found.
 */
exports.status = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, floorId } = req.params;

  const floor = await Floor.findOne({
    where: { floor_id: floorId },
    where: { facility_id: facilityId },
    include: [
      {
        model: Building,
        as: "building",
        attributes: ["facility_id"],
        where: { facility_id: facilityId },
      },
    ],
  });
  if (!floor) {
    return sendError(res, "Floor not found", httpStatus.NOT_FOUND);
  }

  const [updated] = await Floor.update(req.body, {
    where: { floor_id: floorId },
    transaction,
  });

  if (!updated) {
    return sendError(res, "Floor not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Floor status updated successfully", httpStatus.OK);
});
