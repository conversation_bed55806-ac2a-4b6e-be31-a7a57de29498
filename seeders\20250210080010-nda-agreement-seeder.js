"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface) => {
    // First, get the template IDs from the previously seeded templates
    const [templates] = await queryInterface.sequelize.query(
      `SELECT nda_template_id, name, version FROM nda_template ORDER BY created_at;`
    );

    if (templates.length === 0) {
      throw new Error("No NDA templates found. Please run nda-template-seeder first.");
    }

    // Get identity IDs from the identity table
    const [identities] = await queryInterface.sequelize.query(
      `SELECT identity_id FROM identity LIMIT 5;`
    );

    if (identities.length === 0) {
      throw new Error("No identities found. Please ensure identity data exists before running this seeder.");
    }

    // Create agreements using available identities (reuse if necessary)
    const ndaAgreements = [];
    const agreementData = [
      {
        template_index: 0,
        effective_date: new Date("2024-01-15"),
        expiration_date: new Date("2025-01-15"),
        status: 1, // Signed
        signed_at: new Date("2024-01-15T10:30:00Z"),
      },
      {
        template_index: 1,
        effective_date: new Date("2024-02-01"),
        expiration_date: new Date("2025-02-01"),
        status: 1, // Signed
        signed_at: new Date("2024-02-01T14:20:00Z"),
      },
      {
        template_index: 2,
        effective_date: new Date("2024-03-10"),
        expiration_date: new Date("2024-12-31"),
        status: 0, // Pending
        signed_at: null,
      },
      {
        template_index: 3,
        effective_date: new Date("2024-01-01"),
        expiration_date: null,
        status: 1, // Signed
        signed_at: new Date("2024-01-01T09:00:00Z"),
      },
      {
        template_index: 4,
        effective_date: new Date("2023-12-01"),
        expiration_date: new Date("2024-12-01"),
        status: 2, // Expired
        signed_at: new Date("2023-12-01T16:45:00Z"),
      },
    ];

    // Create agreements using available identities and templates
    for (let i = 0; i < agreementData.length; i++) {
      const data = agreementData[i];
      const identityIndex = i % identities.length; // Reuse identities if we have fewer than 5
      const templateIndex = Math.min(data.template_index, templates.length - 1); // Use available templates

      ndaAgreements.push({
        nda_agreement_id: uuidv4(),
        identity_id: identities[identityIndex].identity_id,
        nda_template_id: templates[templateIndex].nda_template_id,
        effective_date: data.effective_date,
        expiration_date: data.expiration_date,
        status: data.status,
        signed_at: data.signed_at,
        created_at: new Date(),
        updated_at: new Date(),
      });
    }

    await queryInterface.bulkInsert("nda_agreement", ndaAgreements, {});
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete("nda_agreement", null, {});
  },
};
