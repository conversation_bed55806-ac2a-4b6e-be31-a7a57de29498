const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Timezone = sequelize.define(
    "Timezone",
    {
      timezone_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      country_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "country",
          key: "country_id",
        },
        onDelete: "CASCADE",
      },
      code: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "timezone",
      timestamps: true,
      underscored: true,
    }
  );

  Timezone.associate = (models) => {
    // Removed association with Country to prevent inclusion of country data
  };

  history(Timezone, sequelize, DataTypes);

  return Timezone;
};