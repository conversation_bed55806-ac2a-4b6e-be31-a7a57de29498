const bcrypt = require('bcrypt');
const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const IdentityVerification = sequelize.define(
    'IdentityVerification',
    {
      identity_verification_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'identity', // Must match the table name of the Identity model
          key: 'identity_id',
        },
      },
      password_hash: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      // Virtual field with a custom setter that hashes the plain text password.
      password_plain: {
        type: DataTypes.VIRTUAL,
        set(value) {
          // Store the plain value (if needed, for validation etc.)
          this.setDataValue('password_plain', value);
          if (value) {
            // Hash the value synchronously or asynchronously (using sync here for simplicity)
            const hash = bcrypt.hashSync(value, 8);
            this.setDataValue('password_hash', hash);
          }
        },
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: 'identity_verification',
      timestamps: true,
      underscored: true,
    }
  );

  // Before saving, if a plain password is provided, hash it and store in password_hash.
  // IdentityVerification.beforeSave(async (instance) => {
  //   if (instance.password_plain) {
  //     const hash = await bcrypt.hash(instance.password_plain, 8);
  //     instance.password_hash = hash;
  //   }
  // });

  /**
   * Instance Method: Check if the provided password matches the stored hash.
   * @param {string} password - The plain-text password to validate.
   * @returns {Promise<boolean>}
   */
  IdentityVerification.prototype.validatePassword = async function (password) {
    return bcrypt.compare(password, this.password_hash);
  };

  // Association: Each IdentityVerification belongs to an Identity.
  IdentityVerification.associate = (models) => {
    IdentityVerification.belongsTo(models.Identity, {
      foreignKey: 'identity_id',
      as: 'identity',
      onDelete: 'CASCADE',
    });
  };

  history(IdentityVerification, sequelize, DataTypes);

  return IdentityVerification;
};
