"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Step 1: Create Kiosk Groups (required for devices)
      const kioskGroups = [
        {
          kiosk_group_id: uuidv4(),
          name: "Main Lobby Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_group_id: uuidv4(),
          name: "Emergency Department Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_group_id: uuidv4(),
          name: "Outpatient Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_group_id: uuidv4(),
          name: "Administrative Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_group_id: uuidv4(),
          name: "Visitor Management Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("kiosk_group", kioskGroups, { transaction });

      // Step 2: Get existing facilities for optional relationships
      const facilities = await queryInterface.sequelize.query(
        "SELECT facility_id FROM facility LIMIT 3;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      let buildings = [];
      let floors = [];
      let rooms = [];

      // Step 3: Create some buildings, floors, and rooms if facilities exist
      if (facilities.length > 0) {
        // Create buildings
        buildings = [
          {
            building_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            name: "Main Hospital Building",
            address: "123 Medical Center Drive",
            year_constructed: 2015,
            building_code: "MHB-001",
            status: 0, // Active
            type: 3, // Institutional
            occupancy_type: 5, // Institutional Occupancy
            phone: "******-0101",
            email: "<EMAIL>",
            geo_location_code: -122.4194,
            other_code: "MHB",
            building_url: "https://hospital.com/buildings/main",
            connected_applications: null,
            notes: "Primary hospital building with emergency and outpatient services",
            updated_by: null,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            building_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            name: "Administrative Building",
            address: "456 Admin Plaza",
            year_constructed: 2018,
            building_code: "ADM-001",
            status: 0, // Active
            type: 1, // Commercial
            occupancy_type: 1, // Business Occupancy
            phone: "******-0102",
            email: "<EMAIL>",
            geo_location_code: -122.4195,
            other_code: "ADM",
            building_url: "https://hospital.com/buildings/admin",
            connected_applications: null,
            notes: "Administrative offices and management",
            updated_by: null,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];

        await queryInterface.bulkInsert("building", buildings, { transaction });

        // Create floors
        floors = [
          {
            floor_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            building_id: buildings[0].building_id,
            floor_number: 1,
            status: 0, // Active
            total_square_footage: 15000.50,
            max_occupancy: 200,
            occupancy_type: 5, // Institutional Occupancy
            updated_by: null,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            floor_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            building_id: buildings[0].building_id,
            floor_number: 2,
            status: 0, // Active
            total_square_footage: 12000.75,
            max_occupancy: 150,
            occupancy_type: 5, // Institutional Occupancy
            updated_by: null,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];

        await queryInterface.bulkInsert("floor", floors, { transaction });

        // Create rooms
        rooms = [
          {
            room_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            building_id: buildings[0].building_id,
            floor_id: floors[0].floor_id,
            room_number: "101",
            max_occupancy: 20,
            area: 500,
            primary_contact_name: "Dr. Sarah Johnson",
            primary_contact_number: "5551234567",
            primary_contact_email: "<EMAIL>",
            status: 0, // Active
            updated_by: null,
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            room_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            building_id: buildings[0].building_id,
            floor_id: floors[0].floor_id,
            room_number: "102",
            max_occupancy: 15,
            area: 350,
            primary_contact_name: "Nurse Manager Lisa Chen",
            primary_contact_number: "5551234568",
            primary_contact_email: "<EMAIL>",
            status: 0, // Active
            updated_by: null,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];

        await queryInterface.bulkInsert("room", rooms, { transaction });
      }

      // Step 4: Create devices
      const devices = [
        {
          device_id: uuidv4(),
          name: "Main Lobby Check-in Kiosk 1",
          identifier: "KIOSK-LOBBY-001",
          kiosk_group_id: kioskGroups[0].kiosk_group_id,
          facility_id: facilities.length > 0 ? facilities[0].facility_id : null,
          facility_building_id: buildings.length > 0 ? buildings[0].building_id : null,
          facility_floor_id: floors.length > 0 ? floors[0].floor_id : null,
          facility_room_id: rooms.length > 0 ? rooms[0].room_id : null,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          device_id: uuidv4(),
          name: "Emergency Department Kiosk",
          identifier: "KIOSK-ED-001",
          kiosk_group_id: kioskGroups[1].kiosk_group_id,
          facility_id: facilities.length > 0 ? facilities[0].facility_id : null,
          facility_building_id: buildings.length > 0 ? buildings[0].building_id : null,
          facility_floor_id: floors.length > 0 ? floors[1].floor_id : null,
          facility_room_id: rooms.length > 0 ? rooms[1].room_id : null,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          device_id: uuidv4(),
          name: "Outpatient Registration Kiosk",
          identifier: "KIOSK-OP-001",
          kiosk_group_id: kioskGroups[2].kiosk_group_id,
          facility_id: facilities.length > 1 ? facilities[1].facility_id : null,
          facility_building_id: null,
          facility_floor_id: null,
          facility_room_id: null,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          device_id: uuidv4(),
          name: "Administrative Office Kiosk",
          identifier: "KIOSK-ADMIN-001",
          kiosk_group_id: kioskGroups[3].kiosk_group_id,
          facility_id: facilities.length > 0 ? facilities[0].facility_id : null,
          facility_building_id: buildings.length > 1 ? buildings[1].building_id : null,
          facility_floor_id: null,
          facility_room_id: null,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          device_id: uuidv4(),
          name: "Visitor Management Kiosk",
          identifier: "KIOSK-VM-001",
          kiosk_group_id: kioskGroups[4].kiosk_group_id,
          facility_id: null,
          facility_building_id: null,
          facility_floor_id: null,
          facility_room_id: null,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("device", devices, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.bulkDelete("device", null, { transaction });
      await queryInterface.bulkDelete("room", null, { transaction });
      await queryInterface.bulkDelete("floor", null, { transaction });
      await queryInterface.bulkDelete("building", null, { transaction });
      await queryInterface.bulkDelete("kiosk_group", null, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
