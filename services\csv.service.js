const fs = require('fs');
const path = require('path');
const { S3 } = require('@aws-sdk/client-s3');
const SftpClient = require('ssh2-sftp-client');
const { BlobServiceClient } = require('@azure/storage-blob');
const axios = require('axios');
const logger = require('../config/logger');
const DOWNLOAD_ROOT = path.resolve(path.join(process.cwd(), 'downloads'));
/**
 * Generate a unique local filename for downloads.
 */
function uniqueFilename(original) {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1e6);
  const ext = path.extname(original);
  const name = path.basename(original, ext);
  return `${name}_${timestamp}_${random}${ext}`;
}

/**
 * Handle Local source for inbound operations
 */
async function listAndDownloadLocal(agent) {
  const downloads = [];
  const settings = agent.settingsObj;

  try {
        const sourceDir = settings.directory_path;
        if (!sourceDir) {
          logger.error(`[Agent] No directory_path setting found for agent ${agent.name}`);
          return downloads;
        }

        // Validate source directory exists
        if (!fs.existsSync(sourceDir)) {
          logger.error(`[Agent] Source directory does not exist: ${sourceDir} for agent ${agent.name}`);
          return downloads;
        }

        // Create archive directory in the same location as source directory
        const archiveDir = path.join(sourceDir, 'archive');
        try {
          if (!fs.existsSync(archiveDir)) {
            fs.mkdirSync(archiveDir, { recursive: true });
            logger.info(`[Agent] Created archive directory: ${archiveDir}`);
          }
        } catch (error) {
          logger.error(`[Agent] Failed to create archive directory ${archiveDir}: ${error.message}`);
          return downloads;
        }

        // Read CSV files from source directory
        let files;
        try {
          files = fs.readdirSync(sourceDir).filter(f => f.endsWith('.csv'));
          logger.info(`[Agent] Found ${files.length} CSV files in ${sourceDir} for agent ${agent.name}`);
        } catch (error) {
          logger.error(`[Agent] Failed to read directory ${sourceDir}: ${error.message}`);
          return downloads;
        }

        // Process each CSV file
        for (const file of files) {
          try {
            const srcPath = path.join(sourceDir, file);
            const localName = uniqueFilename(file);
            const destPath = path.join(DOWNLOAD_ROOT, "local", localName);

            // Ensure download directory exists
            if (!fs.existsSync(path.dirname(destPath))) {
              fs.mkdirSync(path.dirname(destPath), { recursive: true });
            }

            // Copy file to processing location
            fs.copyFileSync(srcPath, destPath);
            logger.info(`[Agent] Copied file ${srcPath} to ${destPath} for processing`);

            // Move original file to archive instead of renaming with .processed
            const archivePath = path.join(archiveDir, file);

            // Handle case where file already exists in archive
            let finalArchivePath = archivePath;
            let counter = 1;
            while (fs.existsSync(finalArchivePath)) {
              const ext = path.extname(file);
              const name = path.basename(file, ext);
              finalArchivePath = path.join(archiveDir, `${name}_${counter}${ext}`);
              counter++;
            }

            fs.renameSync(srcPath, finalArchivePath);
            logger.info(`[Agent] Moved processed file ${srcPath} to archive: ${finalArchivePath}`);

            downloads.push(destPath);
          } catch (error) {
            logger.error(`[Agent] Failed to process file ${file} for agent ${agent.name}: ${error.message}`);
            // Continue processing other files even if one fails
            continue;
          }
        }
  } catch (error) {
    logger.error(`[Agent] Error processing "${agent.name}" [${agent.source}]: ${error.message}`);
  }

  return downloads;
}

/**
 * Handle AWS S3 source for inbound operations
 */
async function listAndDownloadS3(agent) {
  const downloads = [];
  const settings = agent.settingsObj;

  try {
        const accessKeyId = settings.access_key_id;
        const secretAccessKey = settings.secret_access_key;
        const bucketName = settings.bucket_name;
        const region = settings.region || 'us-east-1';

        if (!accessKeyId || !secretAccessKey || !bucketName) {
          logger.error(`[Agent] Missing AWS S3 credentials for agent ${agent.name}`);
          return downloads;
        }

        const s3 = new S3({ region, credentials: { accessKeyId, secretAccessKey } });
        try {
          const list = await s3.listObjectsV2({ Bucket: bucketName });
          const csvObjects = (list.Contents || []).filter(obj => obj.Key.endsWith('.csv'));
          logger.info(`[Agent] Found ${csvObjects.length} CSV files in S3 bucket ${bucketName} for agent ${agent.name}`);

          for (const obj of csvObjects) {
            try {
              // Download file content
              const data = await s3.getObject({ Bucket: bucketName, Key: obj.Key });
              const filename = uniqueFilename(path.basename(obj.Key));
              const filePath = path.join(DOWNLOAD_ROOT, "aws_s3", filename);

              // Ensure local download directory exists
              if (!fs.existsSync(path.dirname(filePath))) {
                fs.mkdirSync(path.dirname(filePath), { recursive: true });
              }

              // Save file locally for processing
              fs.writeFileSync(filePath, data.Body);
              logger.info(`[Agent] Downloaded S3 file ${obj.Key} to ${filePath} for processing`);

              // Move original file to archive folder in S3 instead of adding .processed extension
              const archiveKey = `archive/${path.basename(obj.Key)}`;

              // Handle case where file already exists in S3 archive
              let finalArchiveKey = archiveKey;
              let counter = 1;
              while (true) {
                try {
                  await s3.headObject({ Bucket: bucketName, Key: finalArchiveKey });
                  // File exists, try with counter
                  const ext = path.extname(obj.Key);
                  const name = path.basename(obj.Key, ext);
                  finalArchiveKey = `archive/${name}_${counter}${ext}`;
                  counter++;
                } catch (error) {
                  if (error.name === 'NotFound') {
                    // File doesn't exist, we can use this key
                    break;
                  }
                  throw error; // Re-throw other errors
                }
              }

              // Copy file to archive location and delete original
              await s3.copyObject({
                Bucket: bucketName,
                CopySource: `${bucketName}/${obj.Key}`,
                Key: finalArchiveKey
              });
              await s3.deleteObject({ Bucket: bucketName, Key: obj.Key });
              logger.info(`[Agent] Moved processed S3 file ${obj.Key} to archive: ${finalArchiveKey}`);

              downloads.push(filePath);
            } catch (error) {
              logger.error(`[Agent] Failed to process S3 file ${obj.Key} for agent ${agent.name}: ${error.message}`);
              // Continue processing other files even if one fails
              continue;
            }
          }
        } catch (error) {
          logger.error(`[Agent] S3 operation error for agent ${agent.name}: ${error.message}`);
        }
  } catch (error) {
    logger.error(`[Agent] S3 operation error for agent ${agent.name}: ${error.message}`);
  }

  return downloads;
}

/**
 * Handle Azure Blob source for inbound operations
 */
async function listAndDownloadAzureBlob(agent) {
  const downloads = [];
  const settings = agent.settingsObj;

  try {
        const connectionString = settings.connection_string;
        const containerName = settings.container_name;

        if (!connectionString || !containerName) {
          logger.error(`[Agent] Missing Azure Blob credentials for agent ${agent.name}`);
          return downloads;
        }

        try {
          const client = BlobServiceClient.fromConnectionString(connectionString);
          const container = client.getContainerClient(containerName);

          // Collect CSV blobs first
          const csvBlobs = [];
          for await (const blobItem of container.listBlobsFlat()) {
            if (blobItem.name.endsWith('.csv')) {
              csvBlobs.push(blobItem);
            }
          }
          logger.info(`[Agent] Found ${csvBlobs.length} CSV files in Azure container ${containerName} for agent ${agent.name}`);

          for (const blobItem of csvBlobs) {
            try {
              const blobClient = container.getBlobClient(blobItem.name);

              // Download blob content
              const download = await blobClient.download();
              const chunks = [];
              for await (const chunk of download.readableStreamBody) chunks.push(chunk);
              const buffer = Buffer.concat(chunks);

              // Save file locally for processing
              const filename = uniqueFilename(path.basename(blobItem.name));
              const filePath = path.join(DOWNLOAD_ROOT, "azure_blob", filename);

              // Ensure local download directory exists
              if (!fs.existsSync(path.dirname(filePath))) {
                fs.mkdirSync(path.dirname(filePath), { recursive: true });
              }

              fs.writeFileSync(filePath, buffer);
              logger.info(`[Agent] Downloaded Azure blob ${blobItem.name} to ${filePath} for processing`);

              // Move original blob to archive folder instead of adding .processed extension
              const archiveBlobName = `archive/${path.basename(blobItem.name)}`;

              // Handle case where blob already exists in archive
              let finalArchiveBlobName = archiveBlobName;
              let counter = 1;
              while (true) {
                try {
                  const archiveBlobClient = container.getBlobClient(finalArchiveBlobName);
                  const exists = await archiveBlobClient.exists();
                  if (!exists) {
                    break; // Blob doesn't exist, we can use this name
                  }
                  // Blob exists, try with counter
                  const ext = path.extname(blobItem.name);
                  const name = path.basename(blobItem.name, ext);
                  finalArchiveBlobName = `archive/${name}_${counter}${ext}`;
                  counter++;
                } catch (error) {
                  logger.error(`[Agent] Error checking Azure blob existence: ${error.message}`);
                  break;
                }
              }

              // Copy blob to archive location and delete original
              const archiveBlobClient = container.getBlobClient(finalArchiveBlobName);
              await archiveBlobClient.upload(buffer, buffer.length);
              await blobClient.delete();
              logger.info(`[Agent] Moved processed Azure blob ${blobItem.name} to archive: ${finalArchiveBlobName}`);

              downloads.push(filePath);
            } catch (error) {
              logger.error(`[Agent] Failed to process Azure blob ${blobItem.name} for agent ${agent.name}: ${error.message}`);
              // Continue processing other blobs even if one fails
              continue;
            }
          }
        } catch (error) {
          logger.error(`[Agent] Azure Blob operation error for agent ${agent.name}: ${error.message}`);
        }
  } catch (error) {
    logger.error(`[Agent] Azure Blob operation error for agent ${agent.name}: ${error.message}`);
  }

  return downloads;
}

/**
 * Handle SFTP source for inbound operations
 */
async function listAndDownloadSFTP(agent) {
  const downloads = [];
  const settings = agent.settingsObj;

  try {
        const host = settings.host;
        const port = parseInt(settings.port) || 22;
        const username = settings.username;
        const password = settings.password;
        const remotePath = settings.path;

        if (!host || !username || !password || !remotePath) {
          logger.error(`[Agent] Missing FileTransfer credentials for agent ${agent.name}`);
          return downloads;
        }

        const client = new SftpClient();
        try {
          await client.connect({ host, port, username, password });

          // Create archive directory on remote server
          const remoteArchiveDir = path.posix.join(remotePath, 'archive');
          try {
            const archiveDirExists = await client.exists(remoteArchiveDir);
            if (!archiveDirExists) {
              await client.mkdir(remoteArchiveDir, true);
              logger.info(`[Agent] Created remote archive directory: ${remoteArchiveDir}`);
            }
          } catch (error) {
            logger.error(`[Agent] Failed to create remote archive directory ${remoteArchiveDir}: ${error.message}`);
            await client.end();
            return downloads;
          }

          const files = await client.list(remotePath);
          const csvFiles = files.filter(f => f.name.endsWith('.csv'));
          logger.info(`[Agent] Found ${csvFiles.length} CSV files in remote path ${remotePath} for agent ${agent.name}`);

          for (const file of csvFiles) {
            try {
              const remoteFilePath = path.posix.join(remotePath, file.name);
              const filename = uniqueFilename(file.name);
              const localPath = path.join(DOWNLOAD_ROOT, "ftps", filename);

              // Ensure local download directory exists
              if (!fs.existsSync(path.dirname(localPath))) {
                fs.mkdirSync(path.dirname(localPath), { recursive: true });
              }

              // Download file to local processing location
              await client.fastGet(remoteFilePath, localPath);
              logger.info(`[Agent] Downloaded file ${remoteFilePath} to ${localPath} for processing`);

              // Move original file to archive on remote server instead of renaming with .processed
              const remoteArchivePath = path.posix.join(remoteArchiveDir, file.name);

              // Handle case where file already exists in remote archive
              let finalRemoteArchivePath = remoteArchivePath;
              let counter = 1;
              while (await client.exists(finalRemoteArchivePath)) {
                const ext = path.extname(file.name);
                const name = path.basename(file.name, ext);
                finalRemoteArchivePath = path.posix.join(remoteArchiveDir, `${name}_${counter}${ext}`);
                counter++;
              }

              await client.rename(remoteFilePath, finalRemoteArchivePath);
              logger.info(`[Agent] Moved processed file ${remoteFilePath} to remote archive: ${finalRemoteArchivePath}`);

              downloads.push(localPath);
            } catch (error) {
              logger.error(`[Agent] Failed to process remote file ${file.name} for agent ${agent.name}: ${error.message}`);
              // Continue processing other files even if one fails
              continue;
            }
          }
        } catch (error) {
          logger.error(`[Agent] FileTransfer connection error for agent ${agent.name}: ${error.message}`);
        } finally {
          await client.end();
        }
  } catch (error) {
    logger.error(`[Agent] FileTransfer connection error for agent ${agent.name}: ${error.message}`);
  } finally {
    await client.end();
  }

  return downloads;
}

/**
 * Upload file to local directory
 */
async function uploadToLocal(csvFilePath, settings, fileName) {
  const outputDir = settings.directory_path;
  if (!outputDir) {
    throw new Error('No directory_path setting found for Local destination');
  }

  // Ensure output directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    logger.info(`Created output directory: ${outputDir}`);
  }

  const destinationPath = path.join(outputDir, fileName);

  // Handle file name conflicts
  let finalDestinationPath = destinationPath;
  let counter = 1;
  while (fs.existsSync(finalDestinationPath)) {
    const ext = path.extname(fileName);
    const name = path.basename(fileName, ext);
    finalDestinationPath = path.join(outputDir, `${name}_${counter}${ext}`);
    counter++;
  }

  // Copy file to destination
  fs.copyFileSync(csvFilePath, finalDestinationPath);
  logger.info(`Copied CSV file to local directory: ${finalDestinationPath}`);

  return finalDestinationPath;
}

/**
 * Upload file to AWS S3
 */
async function uploadToS3(csvFilePath, settings, fileName) {
  const accessKeyId = settings.access_key_id;
  const secretAccessKey = settings.secret_access_key;
  const bucketName = settings.bucket_name;
  const region = settings.region || 'us-east-1';
  const s3Path = settings.s3_path || '';

  if (!accessKeyId || !secretAccessKey || !bucketName) {
    throw new Error('Missing AWS S3 credentials (access_key_id, secret_access_key, bucket_name)');
  }

  const s3 = new S3({ region, credentials: { accessKeyId, secretAccessKey } });

  // Construct S3 key
  const s3Key = s3Path ? `${s3Path}/${fileName}` : fileName;

  // Handle key conflicts
  let finalS3Key = s3Key;
  let counter = 1;
  while (true) {
    try {
      await s3.headObject({ Bucket: bucketName, Key: finalS3Key });
      // File exists, try with counter
      const ext = path.extname(fileName);
      const name = path.basename(fileName, ext);
      const basePath = s3Path ? `${s3Path}/${name}` : name;
      finalS3Key = `${basePath}_${counter}${ext}`;
      counter++;
    } catch (error) {
      if (error.name === 'NotFound') {
        // File doesn't exist, we can use this key
        break;
      }
      throw error; // Re-throw other errors
    }
  }

  // Read file and upload
  const fileContent = fs.readFileSync(csvFilePath);
  await s3.putObject({
    Bucket: bucketName,
    Key: finalS3Key,
    Body: fileContent,
    ContentType: 'text/csv'
  });

  const s3Url = `s3://${bucketName}/${finalS3Key}`;
  logger.info(`Uploaded CSV file to S3: ${s3Url}`);
  return s3Url;
}

/**
 * Upload file to Azure Blob Storage
 */
async function uploadToAzureBlob(csvFilePath, settings, fileName) {
  const connectionString = settings.connection_string;
  const containerName = settings.container_name;
  const blobPath = settings.blob_path || '';

  if (!connectionString || !containerName) {
    throw new Error('Missing Azure Blob credentials (connection_string, container_name)');
  }

  const client = BlobServiceClient.fromConnectionString(connectionString);
  const container = client.getContainerClient(containerName);

  // Construct blob name
  const blobName = blobPath ? `${blobPath}/${fileName}` : fileName;

  // Handle blob name conflicts
  let finalBlobName = blobName;
  let counter = 1;
  while (true) {
    try {
      const blobClient = container.getBlobClient(finalBlobName);
      const exists = await blobClient.exists();
      if (!exists) {
        break; // Blob doesn't exist, we can use this name
      }
      // Blob exists, try with counter
      const ext = path.extname(fileName);
      const name = path.basename(fileName, ext);
      const basePath = blobPath ? `${blobPath}/${name}` : name;
      finalBlobName = `${basePath}_${counter}${ext}`;
      counter++;
    } catch (error) {
      logger.error(`Error checking Azure blob existence: ${error.message}`);
      break;
    }
  }

  // Read file and upload
  const fileContent = fs.readFileSync(csvFilePath);
  const blobClient = container.getBlobClient(finalBlobName);
  await blobClient.upload(fileContent, fileContent.length, {
    blobHTTPHeaders: { blobContentType: 'text/csv' }
  });

  const blobUrl = `https://${client.accountName}.blob.core.windows.net/${containerName}/${finalBlobName}`;
  logger.info(`Uploaded CSV file to Azure Blob: ${blobUrl}`);
  return blobUrl;
}

/**
 * Upload file to SFTP server
 */
async function uploadToSFTP(csvFilePath, settings, fileName) {
  const host = settings.host;
  const port = parseInt(settings.port) || 22;
  const username = settings.username;
  const password = settings.password;
  const remotePath = settings.path || '/';

  if (!host || !username || !password) {
    throw new Error('Missing SFTP credentials (host, username, password)');
  }

  const client = new SftpClient();
  try {
    await client.connect({ host, port, username, password });

    // Ensure remote directory exists
    const remoteDir = path.posix.dirname(remotePath);
    if (remoteDir !== '.' && remoteDir !== '/') {
      try {
        const dirExists = await client.exists(remoteDir);
        if (!dirExists) {
          await client.mkdir(remoteDir, true);
          logger.info(`Created remote directory: ${remoteDir}`);
        }
      } catch (error) {
        logger.warn(`Could not create remote directory ${remoteDir}: ${error.message}`);
      }
    }

    // Construct remote file path
    const remoteFilePath = path.posix.join(remotePath, fileName);

    // Handle file name conflicts
    let finalRemoteFilePath = remoteFilePath;
    let counter = 1;
    while (await client.exists(finalRemoteFilePath)) {
      const ext = path.extname(fileName);
      const name = path.basename(fileName, ext);
      finalRemoteFilePath = path.posix.join(remotePath, `${name}_${counter}${ext}`);
      counter++;
    }

    // Upload file
    await client.fastPut(csvFilePath, finalRemoteFilePath);
    logger.info(`Uploaded CSV file to SFTP: ${finalRemoteFilePath}`);
    return finalRemoteFilePath;

  } finally {
    await client.end();
  }
}

/**
 * Source/Destination handlers mapping for inbound and outbound operations
 */
const SOURCE_HANDLERS = {
  'Local': {
    inbound: listAndDownloadLocal,
    outbound: uploadToLocal
  },
  'AWSS3': {
    inbound: listAndDownloadS3,
    outbound: uploadToS3
  },
  'AzureBlob': {
    inbound: listAndDownloadAzureBlob,
    outbound: uploadToAzureBlob
  },
  'FileTransfer': {
    inbound: listAndDownloadSFTP,
    outbound: uploadToSFTP
  },
  'URL': {
    inbound: downloadFromHttp,
    outbound: null // URL uploads not supported
  }
};

/**
 * Generic function to handle inbound/outbound operations using mapping
 */
async function processSource(agent, operation = 'inbound') {
  const handler = SOURCE_HANDLERS[agent.source]?.[operation];

  if (!handler) {
    throw new Error(`${operation} operation not supported for source: ${agent.source}`);
  }

  return await handler(agent);
}

/**
 * List and download CSV files for directory-like sources (backward compatibility)
 */
async function listAndDownload(agent) {
  return await processSource(agent, 'inbound');
}

/**
 * Upload CSV file to various destinations
 */
async function uploadCSVFile(csvFilePath, agent, performanceMonitor) {
  const uploadResults = {
    success: false,
    destination: agent.source,
    originalPath: csvFilePath,
    uploadedPath: null,
    error: null,
    fileSize: 0
  };

  try {
    // Get file stats
    const stats = fs.statSync(csvFilePath);
    uploadResults.fileSize = stats.size;

    const fileName = path.basename(csvFilePath);

    performanceMonitor?.startStep(`Upload to ${agent.source}`, {
      agentName: agent.name,
      destination: agent.source,
      fileName,
      fileSize: uploadResults.fileSize
    });

    // Use the mapping to get the upload handler
    const handler = SOURCE_HANDLERS[agent.source]?.outbound;

    if (!handler) {
      throw new Error(`Upload not supported for destination: ${agent.source}`);
    }

    uploadResults.uploadedPath = await handler(csvFilePath, agent.settingsObj, fileName);
    uploadResults.success = true;
    logger.info(`Successfully uploaded CSV to ${agent.source}: ${uploadResults.uploadedPath}`);

    performanceMonitor?.endStep(`Upload to ${agent.source}`, {
      status: 'success',
      uploadedPath: uploadResults.uploadedPath,
      fileSize: uploadResults.fileSize
    });

  } catch (error) {
    uploadResults.error = error.message;
    logger.error(`Error uploading CSV to ${agent.source}:`, error.message);

    performanceMonitor?.endStep(`Upload to ${agent.source}`, {
      status: 'error',
      error: error.message
    });
  }

  return uploadResults;
}

/**
 * Download a single CSV from URL source.
 */
async function downloadFromHttp(agent) {
  const settings = agent.settingsObj;
  const url = settings.url;

  if (!url) {
    logger.error(`[Agent] No URL setting found for agent ${agent.name}`);
    return [];
  }

  try {
    const response = await axios.get(url, { responseType: 'stream' });
    const filename = uniqueFilename(path.basename(new URL(url).pathname) || 'download.csv');
    const host = new URL(url).hostname;
    const dir = path.join(DOWNLOAD_ROOT, 'urls', host);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    const filePath = path.join(dir, filename);
    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);
    await new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });
    return [filePath];
  } catch (error) {
    logger.error(`[Agent] Error downloading from URL for agent ${agent.name}: ${error.message}`);
    return [];
  }
}

/**
 * Simple validation for agent configurations - just filter out invalid ones
 */
async function validateConfigs(agents) {
  return agents.filter(agent => {
    if (!agent || !agent.name || !agent.source || !agent.settingsObj) {
      logger.warn(`Skipping invalid agent configuration: ${agent?.name || 'unknown'}`);
      return false;
    }
    return true;
  });
}

/**
 * Process CSV files: download, then parse in parallel
 */
async function processCSVFiles(agents, performanceMonitor = null) {
  const tasks = [];
  const processingResults = {
    totalAgents: agents.length,
    totalFilesDownloaded: 0,
    totalFilesProcessed: 0,
    totalFilesFailed: 0,
    agentResults: [],
    sourceBreakdown: {}
  };

  for (const agent of agents) {
    const agentStartTime = Date.now();
    let downloads = [];

    try {
      // Track file access time by source
      const sourceStepName = `File Access - ${agent.source} (${agent.name})`;
      performanceMonitor?.startStep(sourceStepName, {
        agentName: agent.name,
        source: agent.source,
        agentId: agent.agent_id
      });

      // Use the mapping to get the appropriate handler
      downloads = await processSource(agent, 'inbound');

      const downloadResults = {
        filesFound: downloads.length,
        source: agent.source
      };

      performanceMonitor?.endStep(sourceStepName, downloadResults);

      // Update source breakdown
      if (!processingResults.sourceBreakdown[agent.source]) {
        processingResults.sourceBreakdown[agent.source] = {
          agentCount: 0,
          filesDownloaded: 0,
          filesProcessed: 0,
          filesFailed: 0
        };
      }
      processingResults.sourceBreakdown[agent.source].agentCount++;
      processingResults.sourceBreakdown[agent.source].filesDownloaded += downloads.length;
      processingResults.totalFilesDownloaded += downloads.length;

      // Create processing tasks for each downloaded file
      downloads.forEach(filePath => {
        tasks.push((async () => {
          const fileStartTime = Date.now();
          const fileStepName = `File Processing - ${agent.name} - ${path.basename(filePath)}`;

          try {
            performanceMonitor?.startStep(fileStepName, {
              agentName: agent.name,
              filePath,
              source: agent.source,
              batchSize: agent.batch_size
            });

            logger.info(`Processing "${agent.name}" file ${filePath}`);

            // Get the handler directly from the agent
            const handlers = require(path.join(process.cwd(), 'handlers'));
            const handler = handlers[agent.handler];
            if (typeof handler !== 'function') {
              logger.error(`Handler "${agent.handler}" not found for agent "${agent.name}"`);
              processingResults.totalFilesFailed++;
              processingResults.sourceBreakdown[agent.source].filesFailed++;
              performanceMonitor?.endStep(fileStepName, {
                status: 'failed',
                reason: 'Handler not found',
                duration: Date.now() - fileStartTime
              });
              return;
            }

            // Use the agent's batch size for processing
            const agentWithBatch = { ...agent, batch_size: agent.batch_size };
            const handlerResults = await handler({ file: filePath, agent: agentWithBatch, performanceMonitor });

            processingResults.totalFilesProcessed++;
            processingResults.sourceBreakdown[agent.source].filesProcessed++;

            performanceMonitor?.endStep(fileStepName, {
              status: 'success',
              duration: Date.now() - fileStartTime,
              ...handlerResults
            });

            logger.info(`Successfully processed "${agent.name}" file ${filePath} with handler ${agent.handler}`);
          } catch (err) {
            processingResults.totalFilesFailed++;
            processingResults.sourceBreakdown[agent.source].filesFailed++;

            performanceMonitor?.endStep(fileStepName, {
              status: 'error',
              error: err.message,
              duration: Date.now() - fileStartTime
            });

            logger.error(`Error processing "${agent.name}" file ${filePath}:`, err);
          }
        })());
      });

      // Record agent-level results
      processingResults.agentResults.push({
        agentName: agent.name,
        source: agent.source,
        filesDownloaded: downloads.length,
        processingDuration: Date.now() - agentStartTime
      });

    } catch (error) {
      logger.error(`Error processing agent "${agent.name}":`, error);
      processingResults.agentResults.push({
        agentName: agent.name,
        source: agent.source,
        filesDownloaded: 0,
        error: error.message,
        processingDuration: Date.now() - agentStartTime
      });
    }
  }

  // Track parallel file processing
  if (tasks.length > 0) {
    performanceMonitor?.startStep('Parallel File Processing', {
      totalTasks: tasks.length,
      totalAgents: agents.length
    });

    // Run all parsing tasks in parallel
    await Promise.all(tasks);

    performanceMonitor?.endStep('Parallel File Processing', {
      tasksCompleted: tasks.length,
      filesProcessed: processingResults.totalFilesProcessed,
      filesFailed: processingResults.totalFilesFailed
    });
  }

  return processingResults;
}

module.exports = {
  validateConfigs,
  processCSVFiles,
  uploadCSVFile
};
