const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const State = sequelize.define(
    "State",
    {
      state_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      country_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "country",
          key: "country_id",
        },
        onDelete: "CASCADE",
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "state",
      timestamps: true,
      underscored: true,
    }
  );

  State.associate = (models) => {
    State.belongsTo(models.Country, {
      foreignKey: "country_id",
      as: "country",
    });
  };

  history(State, sequelize, DataTypes);

  return State;
};
