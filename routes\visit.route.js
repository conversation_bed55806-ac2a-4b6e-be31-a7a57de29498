const express = require("express");
const validate = require("../middlewares/validate");
const VisitValidation = require("../validations/visit.validation");
const VisitController = require("../controllers/visit.controller");
const catchAsync = require("../utils/catchAsync");

const router = express.Router();

/**
 * @swagger
 * /visits/create-with-guest:
 *   post:
 *     summary: Create a visit, guest, and guest_visit in a single transaction
 *     tags: [Visits]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               facility_id: { type: string, format: uuid }
 *               host_id: { type: string, format: uuid }
 *               escort_id: { type: string, format: uuid }
 *               start_date: { type: string, format: date }
 *               start_time: { type: string }
 *               duration: { type: integer }
 *               guest:
 *                 type: object
 *                 properties:
 *                   first_name: { type: string }
 *                   last_name: { type: string }
 *                   date_of_birth: { type: string, format: date }
 *                   email: { type: string }
 *                   mobile_phone: { type: string }
 *                   image: { type: string }
 *     responses:
 *       201:
 *         description: Visit, Guest, and GuestVisit created successfully
 */
router.post(
  "/create-with-guest",
  validate(VisitValidation.createVisitWithGuest),
  catchAsync(VisitController.createVisitWithGuest)
);

/**
 * @swagger
 * /visits/create-event:
 *   post:
 *     summary: Create an event visit with extra fields and optionally link an existing guest
 *     tags: [Visits]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title: { type: string }
 *               category: { type: string }
 *               start_date: { type: string, format: date }
 *               end_date: { type: string, format: date }
 *               repeat_visit: { type: boolean }
 *               facility_id: { type: string, format: uuid }
 *               access_level_id: { type: string, format: uuid }
 *               host: { type: string, format: uuid }
 *               check_in_instruction: { type: string }
 *               escort_id: { type: string, format: uuid }
 *               send_notification: { type: boolean }
 *               remind_me: { type: boolean }
 *               message_to_visitor: { type: string }
 *               guest_id: { type: string, format: uuid }
 *     responses:
 *       201:
 *         description: Event Visit created successfully
 */
router.post(
  "/create-event",
  validate(VisitValidation.createEventVisit),
  catchAsync(VisitController.createEventVisit)
);

/**
 * @swagger
 * /visits/{visit_id}/summary:
 *   get:
 *     summary: Get visit summary with host/escort names
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID
 *     responses:
 *       200:
 *         description: Visit summary retrieved successfully
 */
router.get(
  "/:visit_id/summary",
  validate(VisitValidation.getVisitSummary),
  catchAsync(VisitController.getVisitSummary)
);

module.exports = router;
