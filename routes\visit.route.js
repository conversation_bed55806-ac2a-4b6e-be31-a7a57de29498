const express = require("express");
const validate = require("../middlewares/validate");
const VisitValidation = require("../validations/visit.validation");
const VisitController = require("../controllers/visit.controller");
const catchAsync = require("../utils/catchAsync");

const router = express.Router();

/**
 * @swagger
 * /visits/create-with-guest:
 *   post:
 *     summary: Create a visit, guest, and guest_visit in a single transaction
 *     tags: [Visits]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               facility_id: { type: string, format: uuid }
 *               host_id: { type: string, format: uuid }
 *               escort_id: { type: string, format: uuid }
 *               start_date: { type: string, format: date }
 *               start_time: { type: string }
 *               duration: { type: integer }
 *               guest:
 *                 type: object
 *                 properties:
 *                   first_name: { type: string }
 *                   last_name: { type: string }
 *                   date_of_birth: { type: string, format: date }
 *                   email: { type: string }
 *                   mobile_phone: { type: string }
 *                   image: { type: string }
 *     responses:
 *       201:
 *         description: Visit, Guest, and GuestVisit created successfully
 */
router.post(
  "/create-with-guest",
  validate(VisitValidation.createVisitWithGuest),
  catchAsync(VisitController.createVisitWithGuest)
);

/**
 * @swagger
 * /visits/create-event:
 *   post:
 *     summary: Create an event visit with extra fields and optionally link an existing guest
 *     tags: [Visits]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title: { type: string }
 *               category: { type: integer }
 *               type: { type: integer }
 *               start_date: { type: string, format: date }
 *               end_date: { type: string, format: date }
 *               repeat_visit: { type: integer }
 *               facility_id: { type: string, format: uuid }
 *               access_level_id: { type: string, format: uuid }
 *               host_id: { type: string, format: uuid }
 *               check_in_instruction: { type: integere }
 *               escort_id: { type: string, format: uuid }
 *               send_notification: {type: string, format: uuid}
 *               remind_me: { type: integer }
 *               status: { type: integer }
 *               message_to_visitor: { type: string }
 *               guest_id: { type: string, format: uuid }
 *     responses:
 *       201:
 *         description: Event Visit created successfully
 */
router.post(
  "/create-event",
  validate(VisitValidation.createEventVisit),
  catchAsync(VisitController.createEventVisit)
);

/**
 * @swagger
 * /visits/{visit_id}/summary:
 *   get:
 *     summary: Get visit summary with host/escort names
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID
 *     responses:
 *       200:
 *         description: Visit summary retrieved successfully
 */
router.get(
  "/:visit_id/summary",
  validate(VisitValidation.getVisitSummary),
  catchAsync(VisitController.getVisitSummary)
);

/**
 * @swagger
 * /visits/{visit_id}:
 *   put:
 *     summary: Update an existing visit
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Updated Meeting Title"
 *               category:
 *                 type: integer
 *                 example: 1
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               repeat_visit:
 *                 type: integer
 *                 example: 0
 *               facility_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               access_level_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               host_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               check_in_instruction:
 *                 type: integer
 *                 example: 1
 *               escort_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               send_notification:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               remind_me:
 *                 type: integer
 *                 example: 1
 *               message_to_visitor:
 *                 type: string
 *                 example: "Please check in at the front desk"
 *               status:
 *                 type: integer
 *                 example: 1
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *             minProperties: 1
 *     responses:
 *       200:
 *         description: Visit updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visit updated successfully"
 *                 data:
 *                   type: object
 *                   description: Updated visit object
 *       400:
 *         description: Bad request - validation error
 *       404:
 *         description: Visit not found
 */
router.put(
  "/:visit_id",
  validate(VisitValidation.updateVisit),
  catchAsync(VisitController.updateVisit)
);

/**
 * @swagger
 * /visits/{visit_id}:
 *   delete:
 *     summary: Delete a visit and its associated guest visits
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID to delete
 *     responses:
 *       200:
 *         description: Visit deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visit deleted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     visit_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     message:
 *                       type: string
 *                       example: "Visit and associated guest visits have been deleted"
 *       404:
 *         description: Visit not found
 *       400:
 *         description: Bad request - deletion failed
 */
router.delete(
  "/:visit_id",
  validate(VisitValidation.deleteVisit),
  catchAsync(VisitController.deleteVisit)
);

module.exports = router;
