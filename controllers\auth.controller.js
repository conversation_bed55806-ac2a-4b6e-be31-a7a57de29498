const { status: httpStatus } = require('http-status');
const catchAsync = require('../utils/catchAsync');

const {
  getUserPermissions,
  loginWithEmailAndPassword,
  logout: logoutUser,
  refreshAuth,
  createActivityLog,
  getLanguagePreference, 
  generateAuthTokens
} = require('../services/auth.service');


const { Identity, IdentityRole, IdentityVerification, Role, LanguagePreference, Language } = require('../models');
const { pick } = require('../utils/helpers');
const { sendSuccess, sendError } = require('../utils/ApiResponse');

/**
 * Registers a new user.
 *
 * @async
 * @function register
 * @param {Object} req - The request object.
 * @param {Object} req.body - The request body containing user details.
 * @param {Object} req.transaction - The transaction object for database operations.
 * @param {Object} res - The response object.
 * @param {Function} next - The next middleware function.
 * @returns {Promise<void>} Sends a response with the created identity and tokens.
 * @throws {Error} Throws an error if the admin role is not found.
 */
const register = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const identity = await Identity.create(
    pick(req.body, ['username', 'email', 'first_name', 'last_name', 'updated_by']),
    { transaction }
  );
  const updated_by  = req.body.updated_by;

  const identityVerification = IdentityVerification.build({
    identity_id: identity.identity_id,
    password_plain: req.body.password,
    updated_by
  });

  await identityVerification.save({ transaction });

  const adminRole = await Role.findOne({
    where: { name: 'admin' },
    transaction,
  });

  if (!adminRole) {
    return sendError(res, 'Admin role not found', httpStatus.NOT_FOUND);
  }

  await IdentityRole.create(
    {
      identity_id: identity.identity_id,
      role_id: adminRole.role_id,
      updated_by
    },
    { transaction }
  );

  const authTokens = await generateAuthTokens(identity, transaction);
  const permissions = await getUserPermissions(identity);
  const language = await getLanguagePreference(identity.identity_id);

  await createActivityLog(identity.identity_id, 'REGISTER', req, transaction);

  sendSuccess(res, 'User registered successfully', httpStatus.CREATED, {
    identity,
    tokens: authTokens,
    permissions,
    languagePreference: language,
  });
});

/**
 * Logs in a user with email and password.
 *
 * @async
 * @function login
 * @param {Object} req - The request object.
 * @param {Object} req.body - The request body containing email and password.
 * @param {Object} req.transaction - The transaction object for database operations.
 * @param {Object} res - The response object.
 * @param {Function} next - The next middleware function.
 * @returns {Promise<void>} Sends a response with the authenticated identity and tokens.
 */
const login = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { email, password } = req.body;

  const identity = await loginWithEmailAndPassword(email, password);
  const authTokens = await generateAuthTokens(identity, transaction);
  const permissions = await getUserPermissions(identity);
  const language = await getLanguagePreference(identity.identity_id);

  await createActivityLog(identity.identity_id, 'CUSTOM_LOGIN', req, transaction);

  sendSuccess(res, 'User logged in successfully', httpStatus.OK, {
    identity,
    tokens: authTokens,
    permissions,
    languagePreference: language,
  });
});

/**
 * Logs out a user by invalidating their refresh token.
 *
 * @async
 * @function logout
 * @param {Object} req - The request object.
 * @param {Object} req.body - The request body containing the refresh token.
 * @param {Object} req.transaction - The transaction object for database operations.
 * @param {Object} res - The response object.
 * @param {Function} next - The next middleware function.
 * @returns {Promise<void>} Sends a response with no content on successful logout.
 */
const logout = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const identity_id = await logoutUser(req.body.refreshToken, transaction);
  await createActivityLog(identity_id, 'LOGOUT', req, transaction);
  sendSuccess(res, 'User logged out successfully', httpStatus.NO_CONTENT);
});

/**
 * Refreshes the authentication tokens for a user.
 *
 * @async
 * @function refreshTokens
 * @param {Object} req - The request object.
 * @param {Object} req.body - The request body containing the refresh token.
 * @param {Object} req.transaction - The transaction object for database operations.
 * @param {Object} res - The response object.
 * @param {Function} next - The next middleware function.
 * @returns {Promise<void>} Sends a response with the new tokens.
 */
const refreshTokens = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const refreshAuthResult = await refreshAuth(req.body.refreshToken, transaction);
  await createActivityLog(refreshAuthResult.identity_id, 'REFRESH_TOKEN', req, transaction);

  sendSuccess(res, 'Tokens refreshed successfully', httpStatus.OK, {
    ...refreshAuthResult.tokens
  });
});

/**
 * Handle SSO Callback
 * @async
 * @function handleSsoCallback
 * @param {Object} req - The request object.
 * @param {Object} req.transaction - The transaction object for database operations.
 * @param {Object} res - The response object.
 * @param {Function} next - The next middleware function.
 * @returns {Promise<void>} Sends a response with the new tokens.
 */
const handleSsoCallback = catchAsync(async (req, res, next) => {
  const provider = req.baseUrl.includes('saml') ? 'saml'
    : req.baseUrl.includes('oidc') ? 'oidc'
    : req.baseUrl.includes('azure') ? 'azure'
    : false;

  if (!provider) return sendError(res, 'Invalid provider', httpStatus.BAD_REQUEST);

  const identity = req.user;
  const authTokens = await generateAuthTokens(identity, req.transaction);
  const permissions = await getUserPermissions(identity);
  const language = await getLanguagePreference(identity.identity_id);

  await createActivityLog(identity.identity_id, `${provider.toUpperCase()}_LOGIN`, req, req.transaction);

  sendSuccess(res, 'Login successful', httpStatus.OK, {
    identity,
    tokens: authTokens,
    permissions,
    languagePreference: language,
  });
});

module.exports = {
  register,
  login,
  logout,
  refreshTokens,
  handleSsoCallback,
};
