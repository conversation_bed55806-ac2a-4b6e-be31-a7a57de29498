const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Function = sequelize.define(
    "Function",
    {
      function_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      queue: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      type: {
        type: DataTypes.ENUM('0', '1'),
        allowNull: false,
        comment: '0=internal, 1=external',
        get() {
          const value = this.getDataValue('type');
          return value == 0 ? "internal" : "external";
        },
        set(val) {
          this.setDataValue('type', val === 'internal' ? 0 : 1);
        }
      },
      application_type_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      display_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "function",
      timestamps: true,
      underscored: true,
      paranoid: true,
    }
  );

  history(Function, sequelize, DataTypes);

  return Function;
};
