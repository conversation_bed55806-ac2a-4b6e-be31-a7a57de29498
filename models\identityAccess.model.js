const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const IdentityAccess = sequelize.define(
    "IdentityAccess",
    {
      identity_access_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      access_level_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "access_level",
          key: "access_level_id",
        },
        onDelete: "CASCADE",
      },
      card_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "card",
          key: "card_id",
        },
        onDelete: "CASCADE",
      },
      identity_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "identity",
          key: "identity_id",
        },
        onDelete: "CASCADE",
      },
      owner_eid:{
        type:DataTypes.STRING(20),
        allowNull:true,
      },
    
      start_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      end_date:{
        type: DataTypes.DATE,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "identity_access",
      timestamps: true,
      underscored: true,
    }
  );

  IdentityAccess.associate = (models)   => {
    IdentityAccess.belongsTo(models.AccessLevel, {
      foreignKey: "access_level_id",
      as: "access_level",
    });
    IdentityAccess.belongsTo(models.Card, {
      foreignKey: "card_id",
      as: "card",
    });
    IdentityAccess.belongsTo(models.Identity, {
      foreignKey: "identity_id",
      as: "identity",
    });

    IdentityAccess.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "identity_access_status_name",
      constraints: false,
      scope: {
        group: "identity_access_status",
      },
    });
  }
  history(IdentityAccess, sequelize, DataTypes);

  return IdentityAccess;
};
