const express = require("express");
const auth = require("../middlewares/auth");
const validate = require("../middlewares/validate");
const DelegatesValidation = require("../validations/delegates.validation");
const DelegatesController = require("../controllers/delegates.controller");
const catchAsync = require("../utils/catchAsync");

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Delegates:
 *       type: object
 *       required:
 *         - name
 *         - task_to_delegate
 *         - start_date
 *         - status
 *         - identity_id
 *       properties:
 *         delegate_id:
 *           type: string
 *           format: uuid
 *           description: Unique identifier for the delegation
 *         name:
 *           type: string
 *           description: Name of the delegation
 *         task_to_delegate:
 *           type: string
 *           description: Description of the task being delegated
 *         start_date:
 *           type: string
 *           format: date
 *           description: Start date of delegation
 *         end_date:
 *           type: string
 *           format: date
 *           description: End date of delegation
 *         status:
 *           type: string
 *           enum: [Active, Inactive]
 *           description: Delegation status
 *         identity_id:
 *           type: string
 *           format: uuid
 *           description: Reference to identity
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * /delegates:
 *   post:
 *     summary: Create a new delegation
 *     tags: [Delegates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - task_to_delegate
 *               - start_date
 *               - status
 *               - identity_id
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Project Management Delegation"
 *               task_to_delegate:
 *                 type: string
 *                 example: "Manage the quarterly project deliverables and team coordination"
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-01"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-03-31"
 *               status:
 *                 type: string
 *                 enum: [Active, Inactive]
 *                 example: "Active"
 *               identity_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       201:
 *         description: Delegation created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.post(
  "/",
  auth("create_delegate"),
  validate(DelegatesValidation.createDelegate),
  catchAsync(DelegatesController.createDelegate)
);

/**
 * @swagger
 * /delegates:
 *   get:
 *     summary: Get all delegations with pagination and filtering
 *     tags: [Delegates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: identity_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by identity ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Active, Inactive]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Delegations retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/",
  auth("view_delegates"),
  validate(DelegatesValidation.getDelegates),
  catchAsync(DelegatesController.getDelegates)
);

/**
 * @swagger
 * /delegates/{delegate_id}:
 *   get:
 *     summary: Get a delegation by ID
 *     tags: [Delegates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: delegate_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Delegation ID
 *     responses:
 *       200:
 *         description: Delegation retrieved successfully
 *       404:
 *         description: Delegation not found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/:delegate_id",
  auth("view_delegates"),
  validate(DelegatesValidation.getDelegateById),
  catchAsync(DelegatesController.getDelegateById)
);

/**
 * @swagger
 * /delegates/{delegate_id}:
 *   put:
 *     summary: Update a delegation by ID
 *     tags: [Delegates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: delegate_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Delegation ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               task_to_delegate:
 *                 type: string
 *               start_date:
 *                 type: string
 *                 format: date
 *               end_date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [Active, Inactive]
 *     responses:
 *       200:
 *         description: Delegation updated successfully
 *       404:
 *         description: Delegation not found
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 */
router.put(
  "/:delegate_id",
  auth("edit_delegate"),
  validate(DelegatesValidation.updateDelegate),
  catchAsync(DelegatesController.updateDelegate)
);

/**
 * @swagger
 * /delegates/{delegate_id}:
 *   delete:
 *     summary: Delete a delegation by ID
 *     tags: [Delegates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: delegate_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Delegation ID
 *     responses:
 *       200:
 *         description: Delegation deleted successfully
 *       404:
 *         description: Delegation not found
 *       401:
 *         description: Unauthorized
 */
router.delete(
  "/:delegate_id",
  auth("delete_delegate"),
  validate(DelegatesValidation.deleteDelegate),
  catchAsync(DelegatesController.deleteDelegate)
);


module.exports = router;
