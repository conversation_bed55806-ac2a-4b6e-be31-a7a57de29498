const Joi = require("joi");

const createVisitWithGuest = {
  body: Joi.object().keys({
    facility_id: Joi.string().uuid().required(),
    host_id: Joi.string().uuid().required(),
    escort_id: Joi.string().uuid().optional().allow(null, ""),
    start_date: Joi.date().required(),
    start_time: Joi.string().required(),
    duration: Joi.number().integer().required(),
    guest: Joi.object().keys({
      first_name: Joi.string().required(),
      last_name: Joi.string().required(),
      date_of_birth: Joi.date().optional().allow(null, ""),
      email: Joi.string().email().required(),
      mobile_phone: Joi.string().optional().allow(null, ""),
      image: Joi.string().optional().allow(null, ""),
    }).required(),
  }),
};

const createEventVisit = {
  body: Joi.object().keys({
    title: Joi.string().required(),
    category: Joi.string().required(),
    start_date: Joi.date().required(),
    end_date: Joi.date().required(),
    repeat_visit: Joi.boolean().optional(),
    facility_id: Joi.string().uuid().required(),
    access_level_id: Joi.string().uuid().required(),
    host: Joi.string().uuid().required(),
    check_in_instruction: Joi.string().optional().allow(null, ""),
    escort_id: Joi.string().uuid().optional().allow(null, ""),
    send_notification: Joi.boolean().optional(),
    remind_me: Joi.boolean().optional(),
    message_to_visitor: Joi.string().optional().allow(null, ""),
    guest_id: Joi.string().uuid().optional(),
  }),
};

const getVisitSummary = {
  params: Joi.object().keys({
    visit_id: Joi.string().uuid().required(),
  }),
};

module.exports = {
  createVisitWithGuest,
  createEventVisit,
  getVisitSummary,
};
