const Joi = require("joi");

const createVisitWithGuest = {
  body: Joi.object().keys({
    facility_id: Joi.string().uuid().required(),
    host_id: Joi.string().uuid().required(),
    escort_id: Joi.string().uuid().optional().allow(null, ""),
    start_date: Joi.date().required(),
    start_time: Joi.string().required(),
    duration: Joi.number().integer().required(),
    guest: Joi.object().keys({
      first_name: Joi.string().required(),
      last_name: Joi.string().required(),
      date_of_birth: Joi.date().optional().allow(null, ""),
      email: Joi.string().email().required(),
      mobile_phone: Joi.string().optional().allow(null, ""),
      image: Joi.string().optional().allow(null, ""),
    }).required(),
  }),
};

const createEventVisit = {
  body: Joi.object().keys({
    title: Joi.string().required(),
    category: Joi.number().integer().required(),
    type: Joi.number().integer().optional().default(0),
    start_date: Joi.date().required(),
    end_date: Joi.date().required(),
    repeat_visit: Joi.number().integer().optional(),
    facility_id: Joi.string().uuid().required(),
    access_level_id: Joi.string().uuid().required(),
    host_id: Joi.string().uuid().required(),
    check_in_instruction: Joi.number().integer().optional(),
    escort_id: Joi.string().uuid().optional().allow(null, ""),
    send_notification: Joi.string().uuid().optional(),
    remind_me: Joi.number().integer().optional(),
    message_to_visitor: Joi.string().optional().allow(null, ""),
    guest_ids: Joi.array().items(Joi.string().uuid()).optional().default([]),
    status: Joi.number().integer().optional(),
  }),
};

const getVisitSummary = {
  params: Joi.object().keys({
    visit_id: Joi.string().uuid().required(),
  }),
};

const updateVisit = {
  params: Joi.object().keys({
    visit_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    title: Joi.string().optional(),
    category: Joi.number().integer().optional(),
    type: Joi.number().integer().optional(),
    start_date: Joi.date().optional(),
    end_date: Joi.date().optional(),
    repeat_visit: Joi.number().integer().optional(),
    facility_id: Joi.string().uuid().optional(),
    access_level_id: Joi.string().uuid().optional(),
    host_id: Joi.string().uuid().optional(),
    check_in_instruction: Joi.number().integer().optional(),
    escort_id: Joi.string().uuid().optional().allow(null, ""),
    send_notification: Joi.string().uuid().optional(),
    remind_me: Joi.number().integer().optional(),
    message_to_visitor: Joi.string().optional().allow(null, ""),
    status: Joi.number().integer().optional(),
    updated_by: Joi.string().uuid().optional(),
  }).min(1), // At least one field must be provided for update
};

const deleteVisit = {
  params: Joi.object().keys({
    visit_id: Joi.string().uuid().required(),
  }),
};

module.exports = {
  createVisitWithGuest,
  createEventVisit,
  getVisitSummary,
  updateVisit,
  deleteVisit,
};
