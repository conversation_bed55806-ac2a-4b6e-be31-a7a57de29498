"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface) => {
    const ndaTemplates = [
      {
        nda_template_id: uuidv4(),
        name: "Standard Employee NDA",
        version: 1,
        document_url: "https://example.com/documents/employee-nda-v1.pdf",
        jurisdiction: "California",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        nda_template_id: uuidv4(),
        name: "Standard Employee NDA",
        version: 2,
        document_url: "https://example.com/documents/employee-nda-v2.pdf",
        jurisdiction: "California",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        nda_template_id: uuidv4(),
        name: "Contractor NDA",
        version: 1,
        document_url: "https://example.com/documents/contractor-nda-v1.pdf",
        jurisdiction: "New York",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        nda_template_id: uuidv4(),
        name: "Vendor Partnership NDA",
        version: 1,
        document_url: "https://example.com/documents/vendor-nda-v1.pdf",
        jurisdiction: "Delaware",
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        nda_template_id: uuidv4(),
        name: "Healthcare Data NDA",
        version: 1,
        document_url: "https://example.com/documents/healthcare-nda-v1.pdf",
        jurisdiction: "Federal",
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];

    await queryInterface.bulkInsert("nda_template", ndaTemplates, {});
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete("nda_template", null, {});
  },
};
