const express = require("express");
const validate = require("../middlewares/validate");
const { BuildingValidation } = require("../validations");
const { BuildingController } = require("../controllers");
const auth = require("../middlewares/auth");
const catchAsync = require("../utils/catchAsync");

const router = express.Router({ mergeParams: true }); // mergeParams to capture facilityId from parent routes

/**
 * @swagger
 * /facility/buildings/{facilityId}:
 *   get:
 *     summary: Get all buildings for a facility (paginated)
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID for which to fetch buildings.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1).
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10).
 *     responses:
 *       200:
 *         description: Paginated list of buildings with facility name.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 20
 *               totalPages: 2
 *               currentPage: 1
 *               data:
 *                 - building_id: "64b8f0e2d123e4567890abcd"
 *                   name: "Main Building"
 *                   building_code: "MB001"
 *                   type: 1
 *                   occupancy_type: 2
 *                   status: 0
 *                   phone: "******-1111"
 *                   email: "<EMAIL>"
 *                   facility_name: "Central Hospital"
 */
router.get("/", auth("view_buildings"), validate(BuildingValidation.facility), catchAsync(BuildingController.index));

/**
 * @swagger
 * /facility/buildings/{facilityId}/{buildingId}:
 *   get:
 *     summary: Get a building by ID for a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: buildingId
 *         required: true
 *         schema:
 *           type: string
 *         description: The building ID.
 *     responses:
 *       200:
 *         description: Building details with facility name.
 *         content:
 *           application/json:
 *             example:
 *               building_id: "64b8f0e2d123e4567890abcd"
 *               name: "Main Building"
 *               building_code: "MB001"
 *               type: 1
 *               occupancy_type: 2
 *               status: 0
 *               phone: "******-1111"
 *               email: "<EMAIL>"
 *               facility_name: "Central Hospital"
 *       404:
 *         description: Building not found.
 */
router.get(
  "/:buildingId",
  auth("building_details"),
  validate(BuildingValidation.building),
  catchAsync(BuildingController.show)
);

/**
 * @swagger
 * /facility/buildings/{facilityId}:
 *   post:
 *     summary: Create a new building for a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID where the building will be created.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "New Building"
 *             address: "123 Building St"
 *             year_constructed: 1990
 *             building_code: "NB123"
 *             type: 1
 *             occupancy_type: 3
 *             status: 0
 *             phone: "******-2222"
 *             email: "<EMAIL>"
 *             geo_location_code: 40.712776
 *             other_code: "OB456"
 *             building_url: "http://newbuilding.com"
 *             connected_applications: "App1,App2"
 *             notes: "Additional notes"
 *     responses:
 *       201:
 *         description: Building created successfully.
 */
router.post("/", auth("create_building"), validate(BuildingValidation.create), catchAsync(BuildingController.create));

/**
 * @swagger
 * /facility/buildings/{facilityId}/{buildingId}:
 *   patch:
 *     summary: Update a building's details
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: buildingId
 *         required: true
 *         schema:
 *           type: string
 *         description: The building ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "Updated Building Name"
 *             building_code: "UB123"
 *             type: 2
 *             occupancy_type: 4
 *             phone: "******-3333"
 *             email: "<EMAIL>"
 *     responses:
 *       200:
 *         description: Building updated successfully.
 */
router.patch("/:buildingId", auth("edit_building"), validate(BuildingValidation.update), catchAsync(BuildingController.update));

/**
 * @swagger
 * /facility/buildings/{facilityId}/{buildingId}/status:
 *   patch:
 *     summary: Change a building's status
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: buildingId
 *         required: true
 *         schema:
 *           type: string
 *         description: The building ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             status: 1
 *     responses:
 *       200:
 *         description: Building status updated successfully.
 */
router.patch("/:buildingId/status", auth("status_of_building"), validate(BuildingValidation.status), catchAsync(BuildingController.status));

module.exports = router;
