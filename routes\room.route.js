const express = require("express");
const validate = require("../middlewares/validate");
const { RoomValidation } = require("../validations");
const { RoomController } = require("../controllers");
const auth = require("../middlewares/auth");
const catchAsync = require("../utils/catchAsync");

const router = express.Router({ mergeParams: true });

/**
 * @swagger
 * /facility/rooms/{facilityId}:
 *   get:
 *     summary: Get all rooms for a facility (paginated)
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID for which to retrieve rooms.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *     responses:
 *       200:
 *         description: Paginated list of rooms including their floor details.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 25
 *               totalPages: 3
 *               currentPage: 1
 *               data:
 *                 - room_id: "64b8f0e2d123e4567890abcd"
 *                   building_id: "74b8f0e2d123e4567890abcd"
 *                   floor_number: 23
 *                   room_number: "101A"
 *                   max_occupancy: 2
 *                   area: 350
 *                   primary_contact_name: "John Doe"
 *                   primary_contact_number: "1234567890"
 *                   primary_contact_email: "<EMAIL>"
 *                   status: 0
 *                   floor: { name: "First Floor" }
 *                   building: { name: "Building 1" }
 */
router.get(
  "/",
  auth("view_rooms"),
  validate(RoomValidation.facility),
  catchAsync(RoomController.index)
);

/**
 * @swagger
 * /facility/rooms/{facilityId}/{roomId}:
 *   get:
 *     summary: Get a single room by its ID within a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: roomId
 *         required: true
 *         schema:
 *           type: string
 *         description: The room ID.
 *     responses:
 *       200:
 *         description: Room details including floor information.
 *         content:
 *           application/json:
 *             example:
 *               room_id: "64b8f0e2d123e4567890abcd"
 *               building_id: "74b8f0e2d123e4567890abcd"
 *               floor_number: 23
 *               room_number: "101A"
 *               max_occupancy: 2
 *               area: 350
 *               primary_contact_name: "John Doe"
 *               primary_contact_number: "1234567890"
 *               primary_contact_email: "<EMAIL>"
 *               status: 0
 *               floor: { name: "First Floor" }
 *               building: { name: "Building 1" }
 *       404:
 *         description: Room not found.
 */
router.get(
  "/:roomId",
  auth("room_details"),
  validate(RoomValidation.room),
  catchAsync(RoomController.show)
);

/**
 * @swagger
 * /facility/rooms/{facilityId}:
 *   post:
 *     summary: Create a new room in a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID where the room will be created.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             floor_id: "64b8f0e2d123e4567890abcd"
 *             building_id: "74b8f0e2d123e4567890abcd"
 *             room_number: "101A"
 *             max_occupancy: 2
 *             area: 350
 *             primary_contact_name: "John Doe"
 *             primary_contact_number: "1234567890"
 *             primary_contact_email: "<EMAIL>"
 *             status: 0
 *     responses:
 *       201:
 *         description: Room created successfully.
 */
router.post(
  "/",
  auth("create_room"),
  validate(RoomValidation.create),
  catchAsync(RoomController.create)
);

/**
 * @swagger
 * /facility/rooms/{facilityId}/{roomId}:
 *   patch:
 *     summary: Update room details within a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: roomId
 *         required: true
 *         schema:
 *           type: string
 *         description: The room ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             room_number: "101B"
 *             floor_id: "64b8f0e2d123e4567890abcd"
 *             building_id: "74b8f0e2d123e4567890abcd"
 *             max_occupancy: 3
 *             area: 375
 *             primary_contact_name: "Jane Doe"
 *             primary_contact_number: "0987654321"
 *             primary_contact_email: "<EMAIL>"
 *             status: 0
 *     responses:
 *       200:
 *         description: Room updated successfully.
 */
router.patch(
  "/:roomId",
  auth("edit_room"),
  validate(RoomValidation.update),
  catchAsync(RoomController.update)
);

/**
 * @swagger
 * /facility/rooms/{facilityId}/{roomId}/status:
 *   patch:
 *     summary: Update room status within a facility
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: facilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: The facility ID.
 *       - in: path
 *         name: roomId
 *         required: true
 *         schema:
 *           type: string
 *         description: The room ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             status: 1
 *     responses:
 *       200:
 *         description: Room status updated successfully.
 */
router.patch(
  "/:roomId/status",
  auth("status_of_room"),
  validate(RoomValidation.status),
  catchAsync(RoomController.status)
);

module.exports = router;
