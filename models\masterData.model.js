const { uniq } = require("lodash");
const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const MasterData = sequelize.define(
    "MasterData",
    {
      master_data_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      group: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      key: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      value: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: false,
      },
    },
    {
      tableName: "master_data",
      timestamps: true, // adds created_at and updated_at (with underscored: true)
      underscored: true,
      paranoid: true, // enables soft delete (adds deleted_at)
      indexes: [
        {
          unique: true,
          fields: ["group", "key"],
        },
      ],
    }
  );

  history(MasterData, sequelize, DataTypes);

  return MasterData;
};
