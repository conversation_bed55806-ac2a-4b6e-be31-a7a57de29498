const express = require("express");
const validate = require("../middlewares/validate");
const { AccessLevelValidation } = require("../validations");
const { AccessLevelController } = require("../controllers");
const auth = require("../middlewares/auth");
const catchAsync = require("../utils/catchAsync");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: AccessLevels
 *   description: Access Level management and retrieval
 */

/**
 * @swagger
 * /access-level:
 *   get:
 *     summary: Get all access levels (paginated)
 *     tags: [AccessLevels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *     responses:
 *       200:
 *         description: Paginated list of access levels.
 */
router.get(
  "/",
  auth("view_access_levels"),
  catchAsync(AccessLevelController.index)
);

/**
 * @swagger
 * /access-level/{accessLevelId}:
 *   get:
 *     summary: Get access level by ID
 *     tags: [AccessLevels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accessLevelId
 *         required: true
 *         schema:
 *           type: string
 *         description: The access level ID
 *     responses:
 *       200:
 *         description: Access level details.
 *       404:
 *         description: Access level not found.
 */
router.get(
  "/:accessLevelId",
  auth("access_level_details"),
  validate(AccessLevelValidation.accessLevel),
  catchAsync(AccessLevelController.show)
);

/**
 * @swagger
 * /access-level:
 *   post:
 *     summary: Create a new access level
 *     tags: [AccessLevels]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "Main Entrance"
 *             description: "Access level for main entrance"
 *             pacs_access_level_id: "PAC123"
 *             system_id: "5ebac534-954b-5413-9806-c11200000001"
 *             status: "active"
 *             access_level_type: "common"
 *             facility_id: "609bda561452242d88d36e37"
 *     responses:
 *       201:
 *         description: Access level created successfully.
 */
router.post(
  "/",
  auth("create_access_level"),
  validate(AccessLevelValidation.create),
  catchAsync(AccessLevelController.create)
);

/**
 * @swagger
 * /access-level/{accessLevelId}:
 *   patch:
 *     summary: Update an existing access level
 *     tags: [AccessLevels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accessLevelId
 *         required: true
 *         schema:
 *           type: string
 *         description: The access level ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: "Updated Entrance"
 *             description: "Updated description"
 *             pacs_access_level_id: "PAC456"
 *             system_id: "5ebac534-954b-5413-9806-c11200000002"
 *             facility_id: "609bda561452242d88d36e38"
 *     responses:
 *       200:
 *         description: Access level updated successfully.
 *       404:
 *         description: Access level not found.
 */
router.patch(
  "/:accessLevelId",
  auth("edit_access_level"),
  validate(AccessLevelValidation.update),
  catchAsync(AccessLevelController.update)
);

/**
 * @swagger
 * /access-level/{accessLevelId}/status:
 *   patch:
 *     summary: Change access level status
 *     tags: [AccessLevels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: accessLevelId
 *         required: true
 *         schema:
 *           type: string
 *         description: The access level ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             status: "inactive"
 *     responses:
 *       200:
 *         description: Access level status updated successfully.
 *       404:
 *         description: Access level not found.
 */
router.patch(
  "/:accessLevelId/status",
  auth("status_of_access_level"),
  validate(AccessLevelValidation.status),
  catchAsync(AccessLevelController.status)
);

module.exports = router;
