const express = require("express");
const validate = require("../middlewares/validate");
const { WatchlistValidation } = require("../validations");
const { WatchlistController } = require("../controllers");
const auth = require("../middlewares/auth");
const catchAsync = require("../utils/catchAsync");
const uploadToBase64 = require("../middlewares/upload");

const router = express.Router({ mergeParams: true });

/**
 * @swagger
 * tags:
 *   name: Watchlist
 *   description: Watchlist management for persons of interest
 */

/**
 * @swagger
 * /watchlist:
 *   post:
 *     summary: Create a new person of interest
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               middle_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               suffix:
 *                 type: string
 *               date_of_birth:
 *                 type: string
 *                 format: date
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 *               host:
 *                 type: string
 *               address:
 *                 type: string
 *               reason:
 *                 type: string
 *               description:
 *                 type: string
 *               status:
 *                 type: integer
 *               image:
 *                 type: string
 *                 format: uri
 *               expiry_date:
 *                 type: string
 *                 format: date
 *     responses:
 *       201:
 *         description: Person of interest created successfully.
 *       400:
 *         description: Invalid input data.
 */
router.post("/", auth("create_watchlist"), validate(WatchlistValidation.createWatchlistValidation), catchAsync(WatchlistController.create));

/**
 * @swagger
 * /watchlist:
 *   get:
 *     summary: Retrieve all persons of interest
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter persons of interest
 *     responses:
 *       200:
 *         description: A list of persons of interest.
 */
router.get("/", auth("view_watchlist"),validate(WatchlistValidation.getWatchlist), catchAsync(WatchlistController.getWatchlists));


/**
 * @swagger
 * /watchlist/details:
 *   get:
 *     summary: Retrieve details of a specific watchlist entry
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: watchlist_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the watchlist entry to retrieve.
 *     responses:
 *       200:
 *         description: Watchlist details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Watchlist details retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     first_name:
 *                       type: string
 *                       example: "John"
 *                     last_name:
 *                       type: string
 *                       example: "Doe"
 *                     image:
 *                       type: string
 *                       example: "https://example.com/image.jpg"
 *                     address:
 *                       type: string
 *                       example: "123 Main St, Anytown, USA"
 *                     status:
 *                       type: string
 *                       example: "Active"
 *                     phone:
 *                       type: string
 *                       example: "+1234567890"
 *       400:
 *         description: Bad request if watchlist_id is missing.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "watchlist_id is required"
 *       404:
 *         description: Watchlist not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "No watchlist found"
 */
router.get(
  '/details',
  auth('view_watchlist'),
  // validate(WatchlistValidation.getWatchlistDetailsValidation), // Add validation
  catchAsync(WatchlistController.details)
);

/**
 * @swagger
 * /watchlist/history/{watchlist_id}:
 *   get:
 *     summary: Retrieve watchlist history by watchlist_id
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: watchlist_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the watchlist entry
 *     responses:
 *       200:
 *         description: Watchlist history retrieved successfully.
 *       404:
 *         description: Watchlist history not found.
 */
router.get(
  "/history/:watchlist_id",
  auth("view_watchlist"),
  validate(WatchlistValidation.getWatchlistHistoryValidation),
  catchAsync(WatchlistController.getWatchlistHistory)
);

/**
 * @swagger
 * /watchlist/history:
 *   get:
 *     summary: Retrieve watchlist history
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Watchlist history retrieved successfully.
 *       404:
 *         description: Watchlist history not found.
 */
router.get(
  "/history",
  auth("view_watchlist"),
  validate(WatchlistValidation.getAllWatchlistHistory),
  catchAsync(WatchlistController.getAllWatchlistHistory)
);

/**
 * @swagger
 * /watchlist/{id}:
 *   get:
 *     summary: Retrieve a person of interest by ID
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the person of interest
 *     responses:
 *       200:
 *         description: Person of interest details.
 *       404:
 *         description: Person of interest not found.
 */
router.get("/:id", auth("view_watchlist"), validate(WatchlistValidation.getWatchlistValidation),  catchAsync(WatchlistController.getWatchlistById));

/**
 * @swagger
 * /watchlist/{id}:
 *   put:
 *     summary: Update a person of interest
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the person of interest
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               date_of_birth:
 *                 type: string
 *                 format: date
 *               nationality:
 *                 type: string
 *               expiry_date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [active, inactive, watch, cleared]
 *               risk_level:
 *                 type: string
 *                 enum: [low, medium, high, critical]
 *     responses:
 *       200:
 *         description: Person of interest updated successfully.
 *       404:
 *         description: Person of interest not found.
 */
router.put("/:id", auth("edit_watchlist"), validate(WatchlistValidation.updateWatchlistValidation), catchAsync(WatchlistController.updateWatchlist));

/**
 * @swagger
 * /watchlist/{id}:
 *   delete:
 *     summary: Delete a person of interest
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the person of interest
 *     responses:
 *       204:
 *         description: Person of interest deleted successfully.
 *       404:
 *         description: Person of interest not found.
 */
router.delete("/:id", auth("delete_watchlist"), validate(WatchlistValidation.deleteWatchlistValidation), catchAsync(WatchlistController.deleteWatchlist));

router.patch(
  "/:id/address",
  auth("edit_watchlist"),
  validate(WatchlistValidation.updateWatchlistValidation),
  catchAsync(WatchlistController.updateWatchlist)
);

/**
 * @swagger
 * /watchlist/{watchlist_id}/image:
 *   patch:
 *     summary: Update the image of a person of interest
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: watchlist_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the person of interest
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         description: Watchlist image updated successfully.
 *       404:
 *         description: Watchlist entry not found.
 */
/**
 * @swagger
 * /watchlist/{watchlist_id}/image:
 *   patch:
 *     summary: Update the image of a person of interest
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: watchlist_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the person of interest
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         description: Watchlist image updated successfully.
 *       404:
 *         description: Watchlist entry not found.
 */

router.patch(
  "/:watchlist_id/image",
  auth("edit_watchlist"),
  uploadToBase64("image"),
  validate(WatchlistValidation.updateWatchlistImage),
  catchAsync(WatchlistController.image)
);

/**
 * @swagger
 * /watchlist/{watchlist_id}/document:
 *   post:
 *     summary: Create a document for a watchlist entry
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: watchlist_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the watchlist entry
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - image
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name/title of the document
 *                 example: "ID Document"
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Document/image file to upload
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - image
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name/title of the document
 *                 example: "ID Document"
 *               image:
 *                 type: string
 *                 description: Base64 encoded document/image
 *                 example: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
 *     responses:
 *       201:
 *         description: Watchlist document created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Watchlist document created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     watchlist_id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *                     documents:
 *                       type: string
 *       400:
 *         description: Invalid input data.
 *       404:
 *         description: Watchlist entry not found.
 */
router.post(
  "/:watchlist_id/document",
  auth("create_watchlist"),
  uploadToBase64("image"),
  validate(WatchlistValidation.createWatchlistDocument),
  catchAsync(WatchlistController.document)
);

/**
 * @swagger
 * /watchlist/{watchlist_id}/document:
 *   get:
 *     summary: Get document for a watchlist entry
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: watchlist_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the watchlist entry
 *     responses:
 *       200:
 *         description: Watchlist document retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Watchlist document retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     watchlist_document_id:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     watchlist_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     name:
 *                       type: string
 *                       example: "ID Document"
 *                     documents:
 *                       type: string
 *                       example: "789e0123-e89b-12d3-a456-************"
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-15T10:30:00Z"
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-15T14:45:00Z"
 *       404:
 *         description: Watchlist document not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Watchlist document not found"
 */
router.get(
  "/:watchlist_id/document",
  auth("view_watchlist"),
  validate(WatchlistValidation.getWatchlistDocument),
  catchAsync(WatchlistController.getdocument)
);

/**
 * @swagger
 * /watchlist/document/{watchlist_document_id}:
 *   delete:
 *     summary: Delete a specific watchlist document by document ID
 *     tags: [Watchlist]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: watchlist_document_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the watchlist document to delete
 *     responses:
 *       200:
 *         description: Watchlist document deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Watchlist document deleted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     watchlist_document_id:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     deleted_count:
 *                       type: integer
 *                       example: 1
 *       404:
 *         description: Watchlist document not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Watchlist document not found"
 */
router.delete(
  "/document/:watchlist_document_id",
  auth("delete_watchlist"),
  validate(WatchlistValidation.deleteWatchlistDocument),
  catchAsync(WatchlistController.deletedocument)
);



module.exports = router;