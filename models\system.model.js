const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
    const System = sequelize.define(
      "System",
      {
        system_id: {
          type: DataTypes.UUID,
          primaryKey: true,
          defaultValue: DataTypes.UUIDV4,
        },
        name: {
          type: DataTypes.STRING(100),
          allowNull: false,
        },
        system_pacs_id: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
          },
        },
        description: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        updated_by: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      {
        tableName: "system",
        timestamps: true,
        underscored: true,
      }
    );
  
    history(System, sequelize, DataTypes, []);

    return System;
  };
  