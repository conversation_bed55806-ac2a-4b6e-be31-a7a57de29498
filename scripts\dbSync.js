"use strict";

const fs = require("fs");
const path = require("path");
const { DataTypes } = require("sequelize");
const { titleCase } = require("../utils/helpers");
const sequelize = require("../config/database");
const logger = require("../config/logger");

// Determine lifecycle and refresh flag
const isRefresh = process.argv.includes('refresh');

// Parse CLI args for --model <ModelName> [<ModelName>...]
const args = process.argv.slice(2);
const modelFlagIndex = args.indexOf("--model");
const modelNames = modelFlagIndex > -1
  // collect everything after `--model` until the next --something
  ? args.slice(modelFlagIndex + 1).filter(a => !a.startsWith("--"))
  : [];

// Prepare containers
const models = {};
const modelsDir = path.resolve(__dirname, "../models");

// Load all models
fs.readdirSync(modelsDir)
  .filter((file) => file.endsWith(".model.js"))
  .forEach((file) => {
    const name = titleCase(path.basename(file, ".model.js"));
    const factory = require(path.join(modelsDir, file));
    models[name] = factory(sequelize, DataTypes);
    // logger.info(`Loaded model: ${name}`);
  });


// Wire up associations
Object.values(models).forEach((mdl) => {
  if (typeof mdl.associate === "function") {
    mdl.associate(models);
  }
});

// Sync
(async () => {
  try {
    const syncOptions = {};
    if (isRefresh) {
      syncOptions.force = true;
      logger.info("Running in refresh mode: dropping and recreating tables");
    } else {
      logger.info("Running in normal mode: syncing without only those which are not created already");
    }

    if (modelNames.length) {
      modelNames.forEach(name => {
        if (!models[name]) {
          logger.error(`No model found for “${name}”`);
          process.exit(1);
        }
      });

      // sync each one in sequence
      for (const name of modelNames) {
        logger.info(`Syncing ${name} only…`);
        // if you want all of them forced, pass force: true
        await models[name].sync(syncOptions);
      }
    } else {
      logger.info("Syncing all models…");
      await sequelize.sync(syncOptions);
    }

    logger.info("Sync completed successfully");
    process.exit(0);
  } catch (err) {
    logger.error("Sync failed:", err);
    process.exit(1);
  }
})();
