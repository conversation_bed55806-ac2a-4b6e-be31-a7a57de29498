const history = require("../utils/plugins/history_plugin");

module.exports = (sequelize, DataTypes) => {
  const Country = sequelize.define(
    "Country",
    {
      country_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "country",
      timestamps: true,
      underscored: true,
    }
  );

  history(Country, sequelize, DataTypes);

  return Country;
};