const { System } = require("../models");
const catchAsync = require("../utils/catchAsync");
const { paginate } = require("../utils/plugins/paginate");
const { sendSuccess, sendError } = require("../utils/ApiResponse");
const { status: httpStatus } = require("http-status");

/**
 * @desc    Get all systems with pagination
 * @route   GET /system
 * @access  Protected (Requires appropriate auth role)
 */
exports.index = catchAsync(async (req, res, next) => {
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };
  const queryOptions = {
    order: [["updatedAt", "DESC"]],
  };
  // Retrieve paginated systems. Adjust the query options if you want to include associations.
  const result = await paginate(System, queryOptions, paginationOptions);

  return sendSuccess(
    res,
    "Systems retrieved successfully",
    httpStatus.OK,
    result
  );
});

/**
 * @desc    Get a system by ID
 * @route   GET /system/:systemId
 * @access  Protected (Requires appropriate auth role)
 */
exports.show = catchAsync(async (req, res, next) => {
  const { systemId } = req.params;

  // Find system by its primary key
  const system = await System.findByPk(systemId);
  if (!system) {
    return sendError(res, "System not found", httpStatus.NOT_FOUND);
  }

  return sendSuccess(
    res,
    "System retrieved successfully",
    httpStatus.OK,
    system
  );
});

/**
 * @desc    Create a new system
 * @route   POST /system
 * @access  Protected (Requires appropriate auth role)
 */
exports.create = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const system = await System.create(req.body, { transaction });

  return sendSuccess(
    res,
    "System created successfully",
    httpStatus.CREATED,
    system
  );
});

/**
 * @desc    Update an existing system
 * @route   PATCH /system/:systemId
 * @access  Protected (Requires appropriate auth role)
 */
exports.update = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { systemId } = req.params;

  // Update the system based on the provided values
  const [updated] = await System.update(
    req.body,
    { where: { system_id: systemId }, individualHooks: true },

    { transaction }
  );

  if (!updated) {
    return sendError(res, "System not found", httpStatus.NOT_FOUND);
  }

  return sendSuccess(res, "System updated successfully", httpStatus.OK);
});
