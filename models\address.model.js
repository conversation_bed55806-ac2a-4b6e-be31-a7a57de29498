const history = require("../utils/plugins/history_plugin");
 
module.exports = (sequelize, DataTypes) => {
    const Address = sequelize.define(
      "Address",
      {
        address_id: {
          type: DataTypes.UUID,
          primaryKey: true,
          defaultValue: DataTypes.UUIDV4,
        },
        facility_id: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: "facility",
            key: "facility_id",
          },
        },
        address_line_1: {
          type: DataTypes.STRING,
          allowNull: false,
        },
        address_line_2: {
          type: DataTypes.STRING,
        },
        country_id: {
          type: DataTypes.UUID,
          allowNull:true,
        },
       
        state_id: {
          type: DataTypes.UUID,
          allowNull:true,
        },
        postal_code: {
          type: DataTypes.STRING,
        },
        map_url: {
          type: DataTypes.STRING,
        },
        region: {
          type: DataTypes.STRING,
        },
        updated_by: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      },
      {
        tableName: "address",
        timestamps: true,
        underscored: true,
      }
    );
 
    Address.associate = (models) => {
      Address.belongsTo(models.Facility, {
        foreignKey: "facility_id",
        as: "facility",
        onDelete: "CASCADE",
      });
      Address.belongsTo(models.Country, {
        foreignKey: "country_id",
        as: "country",
      });
      Address.belongsTo(models.State, {
        foreignKey: "state_id",
        as: "state",
      });
    };
 
    history(Address, sequelize, DataTypes);
 
    return Address;
  };