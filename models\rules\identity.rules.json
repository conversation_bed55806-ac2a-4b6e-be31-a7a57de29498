[{"name": "Identity Created Notification", "conditions": {"all": [{"fact": "function", "operator": "equal", "value": "parseData"}, {"fact": "email", "operator": "notEqual", "value": null}]}, "event": {"type": "notification", "params": {"notification": "Identity Created Notification"}}, "order": 1}, {"name": "Identity Updated Notification", "conditions": {"all": [{"fact": "function", "operator": "equal", "value": "parseData"}, {"fact": "email", "operator": "notEqual", "value": null}]}, "event": {"type": "notification", "params": {"notification": "Identity Updated Notification"}}, "order": 1}]